import {
    EventModality,
    EventModalityLabels,
    EventStage,
    EventStageLabels,
} from "@/features/crm/types/event";
import {
    EventSchedule,
    UpdateEventScheduleFormValues,
} from "@/features/crm/types/event-schedule";
import { Button, DatePicker, Form, Input, Select, Radio, message } from "antd";
import { Save, Trash } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectInstructor from "@/features/crm/components/molecules/select-instructor";
import { updateEventSchedule } from "../../services/portals/event-schedule";
import dayjs from "dayjs";
import SelectPartnership from "../molecules/select-partnership";
import PartnershipEnrollmentUrlsList from "../molecules/partnership-enrollment-urls-list";
import GeneralEnrollmentUrl from "../molecules/general-enrollment-url";
import ExternalEventLink from "../molecules/external-event-link";
import UploadEventScheduleThumbnail from "../molecules/upload-event-schedule-thumbnail";
import UploadEventScheduleCover from "../molecules/upload-event-schedule-cover";

const { TextArea } = Input;

type GeneralEventDetailProps = {
    eventSchedule: EventSchedule;
};

export default function GeneralEventDetail({ eventSchedule }: GeneralEventDetailProps) {
    const [form] = Form.useForm<UpdateEventScheduleFormValues>();
    const queryClient = useQueryClient();

    const { mutate: onEventUpdate, isPending: isEventUpdateLoading } = useMutation({
        mutationFn: (partialEvent: Partial<UpdateEventScheduleFormValues>) =>
            updateEventSchedule(eventSchedule.esid, partialEvent),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ["event-schedule", eventSchedule.esid],
            });
            message.success("El evento ha sido actualizado correctamente");
        },
    });

    const handleFormFinish = (values: UpdateEventScheduleFormValues) => {
        onEventUpdate(values);
    };

    const modalitySelectOptions = Object.values(EventModality).map((value) => ({
        value,
        label: EventModalityLabels[value],
    }));

    const stageSelectOptions = Object.values(EventStage).map((value) => ({
        value,
        label: EventStageLabels[value],
    }));

    return (
        <Form
            name="generalEventForm"
            layout="vertical"
            form={form}
            initialValues={{
                name: eventSchedule.name,
                description: eventSchedule.description,
                modality: eventSchedule.modality,
                stage: eventSchedule.stage,
                instructor: eventSchedule.instructor?.iid,
                location: eventSchedule.location,
                price: eventSchedule.price,
                partnerships: eventSchedule.partnerships.map((p) => p.pid),
                isGeneral: eventSchedule.isGeneral,
                rangeDate: [
                    dayjs(eventSchedule.startDate),
                    dayjs(eventSchedule.endDate),
                ],
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-x-4">
                        <Form.Item<UpdateEventScheduleFormValues>
                            name="name"
                            label={<FormLabel>Nombre del evento</FormLabel>}
                            className="col-span-2"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el nombre del evento.",
                                },
                            ]}
                        >
                            <Input placeholder="Ej. ¿Cómo ingresar a los CEUs?" />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="modality"
                            label={<FormLabel>Modalidad</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la modalidad.",
                                },
                            ]}
                        >
                            <Select options={modalitySelectOptions} />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="stage"
                            label={<FormLabel>Etapa</FormLabel>}
                            className="col-span-2"
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la etapa.",
                                },
                            ]}
                        >
                            <Radio.Group
                                optionType="button"
                                buttonStyle="solid"
                                options={stageSelectOptions}
                                className="w-full"
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="isGeneral"
                            label={<FormLabel>Tipo de evento</FormLabel>}
                            tooltip="Si el horario se marca como general aparecerá en la website"
                        >
                            <Radio.Group
                                optionType="button"
                                buttonStyle="solid"
                                options={[
                                    { value: false, label: "Específico" },
                                    { value: true, label: "General" },
                                ]}
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="partnerships"
                            label={<FormLabel>Alianzas</FormLabel>}
                            className="col-span-3"
                        >
                            <SelectPartnership />
                        </Form.Item>

                        <PartnershipEnrollmentUrlsList
                            partnershipEnrollmentUrls={
                                eventSchedule.partnershipEnrollmentUrls
                            }
                            className="col-span-3 mb-4"
                        />

                        <GeneralEnrollmentUrl
                            enrollmentUrl={eventSchedule.enrollmentUrl}
                            isGeneral={eventSchedule.isGeneral}
                            className="col-span-3 mb-4"
                        />

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="description"
                            label={<FormLabel>Descripción</FormLabel>}
                            className="col-span-3"
                        >
                            <TextArea
                                rows={4}
                                placeholder="Descripción del evento..."
                            />
                        </Form.Item>

                        <Form.Item<UpdateEventScheduleFormValues>
                            name="location"
                            label={<FormLabel>Ubicación</FormLabel>}
                        >
                            <Input placeholder="Ej. Zoom, Google Meet, etc." />
                        </Form.Item>
                        <Form.Item
                            name="instructor"
                            label={<FormLabel>Instructor</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un instructor.",
                                },
                            ]}
                        >
                            <SelectInstructor />
                        </Form.Item>
                    </div>
                </div>

                <div className="col-span-2 space-y-2">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                block
                            >
                                Eliminar
                            </Button>
                            <Button
                                block
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isEventUpdateLoading}
                                onClick={() => form.submit()}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">FECHAS</p>
                        <div>
                            <Form.Item<UpdateEventScheduleFormValues>
                                name="rangeDate"
                                label={<FormLabel>Fecha de Inicio/Fin</FormLabel>}
                            >
                                <DatePicker.RangePicker showTime className="w-full" />
                            </Form.Item>
                        </div>
                    </div>

                    <ExternalEventLink extEventLink={eventSchedule.extEventLink} />

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <div>
                            <FormLabel>Miniatura</FormLabel>
                            <UploadEventScheduleThumbnail
                                esid={eventSchedule.esid}
                                initialEventScheduleThumbnail={eventSchedule.thumbnail}
                            />
                        </div>

                        <div>
                            <FormLabel>Portada</FormLabel>
                            <UploadEventScheduleCover
                                esid={eventSchedule.esid}
                                initialEventScheduleCoverImage={
                                    eventSchedule.coverImage
                                }
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
}
