import {
    CreateEventReminderBody,
    EventReminder,
    PartialUpdateEventReminderBody,
} from "@myTypes/event-reminder";
import { portalsApi } from "@services/portals";
import dayjs from "dayjs";

export type ListEventReminderQuery = {
    page?: number;
    pageSize?: number;
    search?: string;
    sortBy?: string;
    order?: "asc" | "desc";
};

export const listEventReminder = async (params: ListEventReminderQuery) => {
    const response = await portalsApi.get("crm/event-reminders", {
        params,
    });
    return response.data;
};

export const createEventReminder = async (data: CreateEventReminderBody) => {
    const dayjsDate = dayjs(data.sendAt);
    const normalDate: Date = dayjsDate.toDate();
    data.sendAt = normalDate;
    const response = await portalsApi.post("crm/event-reminders", data);
    return response.data;
};

export const retrieveEventReminder = async (rid: string): Promise<EventReminder> => {
    const response = await portalsApi.get(`crm/event-reminders/${rid}`);
    return response.data;
};

export const deleteEventReminder = async (rid: string) => {
    const response = await portalsApi.delete(`crm/event-reminders/${rid}`);
    return response.data;
};

export const updateEventReminder = async (rid: string, data: PartialUpdateEventReminderBody) => {
    const dayjsDate = dayjs(data.sendAt);
    const normalDate: Date = dayjsDate.toDate();
    data.sendAt = normalDate;
    const response = await portalsApi.patch(`crm/event-reminders/${rid}`, data);
    return response.data;
};

export const sendEventReminder = async (rid: string) => {
    const response = await portalsApi.post(`crm/event-reminders/${rid}/send`);
    return response.data;
};