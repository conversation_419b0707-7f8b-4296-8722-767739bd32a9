import { getTerms, createTerm } from "@/features/crm/services/portals/term";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { Button, Divider, Form, Input, Modal, Select, SelectProps, App } from "antd";
import { Plus } from "lucide-react";
import { useState } from "react";

interface SelectMajorProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectMajor({
    value,
    onChange,
    ...restProps
}: SelectMajorProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    // Query to fetch educational institutions
    const { data, isLoading } = useQuery({
        queryKey: ["terms"],
        queryFn: async () => getTerms(),
    });

    // Mutation for creating a new major
    const { mutate: createTermMutation, isPending: isCreating } = useMutation({
        mutationFn: createTerm,
        onSuccess: (newTerm) => {
            notification.success({
                message: "Ciclo académico creado",
                description: "El ciclo académico ha sido creado exitosamente",
            });
            setIsModalOpen(false);
            form.resetFields();
            // Invalidate and refetch majors
            queryClient.invalidateQueries({ queryKey: ["terms"] });
            // Select the newly created major
            if (onChange) {
                onChange(newTerm.tid);
            }
        },
        onError: () => {
            notification.error({
                message: "Error al crear la carrera profesional",
                description:
                    "Ha ocurrido un error al intentar crear el ciclo académico",
            });
        },
    });

    const { results: terms } = data || {
        results: [],
    };

    const termsOptions: SelectProps["options"] =
        terms?.map((term) => ({
            value: term.tid,
            label: term.name,
        })) || [];

    // Handle change in a way that works with both Form.Item and direct usage
    const handleChange = (selectedValue: string) => {
        if (onChange) {
            onChange(selectedValue);
        }
    };

    const handleCreateTerm = async (values: { name: string }) => {
        createTermMutation(values.name);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={handleChange}
                options={termsOptions}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras el ciclo académico?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setIsModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />

            <Modal
                title="Agregar Ciclo Académico"
                open={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                footer={null}
                confirmLoading={isCreating}
            >
                <Form form={form} onFinish={handleCreateTerm} layout="vertical">
                    <Form.Item
                        name="name"
                        label="Nombre del Ciclo Académico"
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor ingrese el nombre del ciclo académico",
                            },
                        ]}
                    >
                        <Input
                            placeholder="Ingrese el nombre del ciclo académico"
                            disabled={isCreating}
                        />
                    </Form.Item>
                    <Form.Item className="mb-0 text-right">
                        <Button
                            type="default"
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                            className="mr-2"
                            disabled={isCreating}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isCreating}>
                            Guardar
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}
