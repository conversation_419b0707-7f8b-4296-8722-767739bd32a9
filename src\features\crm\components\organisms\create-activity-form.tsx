import { Button, Form, Input, Select, DatePicker, Card } from "antd";
import {
    ActivityStatus,
    ACTIVITY_STATUS_CHOICES,
    ActivityCreate,
} from "../../types/activity";
import { RetrieveOrder } from "../../types/order";
import { useCreateActivity } from "../../hooks/use-activity";
import SelectOrder from "../molecules/select-order";
import SelectStaffUser from "../molecules/select-staff-user";
import { Save, X } from "lucide-react";
import dayjs, { Dayjs } from "dayjs";

const { TextArea } = Input;

interface CreateActivityFormProps {
    onFinish?: () => void;
    onCancel?: () => void;
    preselectedOrder?: RetrieveOrder;
}

interface ActivityFormData {
    title: string;
    description?: string;
    deadline?: Dayjs;
    responsible?: string;
    status: ActivityStatus;
    order?: string;
}

const CreateActivityForm = ({
    onFinish,
    onCancel,
    preselectedOrder,
}: CreateActivityFormProps) => {
    const [form] = Form.useForm<ActivityFormData>();
    const createActivityMutation = useCreateActivity({
        onCreateActivitySuccess: () => {
            form.resetFields();
            onFinish?.();
        },
    });

    const handleSubmit = (values: ActivityFormData) => {
        const activityData: ActivityCreate = {
            title: values.title,
            description: values.description || null,
            deadline: values.deadline ? values.deadline.format("YYYY-MM-DD") : null,
            responsible: values.responsible || null,
            status: values.status,
            order: values.order || null,
        };

        createActivityMutation.mutate(activityData);
    };

    return (
        <Card>
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                    status: ActivityStatus.PENDING,
                    order: preselectedOrder?.oid, // Preselect the order if provided
                }}
                className="space-y-4"
            >
                <Form.Item
                    label="Título"
                    name="title"
                    rules={[
                        {
                            required: true,
                            message: "Por favor ingresa un título para la actividad",
                        },
                    ]}
                >
                    <Input
                        placeholder="Ej. Llamada de seguimiento al cliente"
                        size="large"
                    />
                </Form.Item>

                <Form.Item label="Descripción" name="description">
                    <TextArea
                        placeholder="Descripción detallada de la actividad..."
                        rows={3}
                        size="large"
                    />
                </Form.Item>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <Form.Item
                        label="Estado"
                        name="status"
                        rules={[
                            {
                                required: true,
                                message: "Por favor selecciona un estado",
                            },
                        ]}
                    >
                        <Select
                            placeholder="Seleccionar estado"
                            size="large"
                            options={ACTIVITY_STATUS_CHOICES.map((choice) => ({
                                value: choice.value,
                                label: choice.label,
                            }))}
                        />
                    </Form.Item>

                    <Form.Item label="Fecha límite" name="deadline">
                        <DatePicker
                            placeholder="Seleccionar fecha límite"
                            size="large"
                            format="DD/MM/YYYY"
                            className="w-full"
                            disabledDate={(current) =>
                                current && current < dayjs().startOf("day")
                            }
                        />
                    </Form.Item>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <Form.Item label="Responsable" name="responsible">
                        <SelectStaffUser
                            placeholder="Asignar responsable"
                            size="large"
                        />
                    </Form.Item>

                    <Form.Item label="Orden relacionada" name="order">
                        <SelectOrder
                            placeholder="Seleccionar orden"
                            size="large"
                            disabled={!!preselectedOrder} // Disable if order is preselected
                        />
                    </Form.Item>
                </div>

                <div className="flex gap-3 pt-4">
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={createActivityMutation.isPending}
                        icon={<Save size={16} />}
                        size="large"
                    >
                        Crear actividad
                    </Button>
                    {onCancel && (
                        <Button
                            type="default"
                            onClick={onCancel}
                            icon={<X size={16} />}
                            size="large"
                        >
                            Cancelar
                        </Button>
                    )}
                </div>
            </Form>
        </Card>
    );
};

export default CreateActivityForm;
