import { A<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Switch, Typography } from "antd";
import { CreateOrderFormValues, Order, OrderStage } from "@/features/crm/types/order";
import SelectContact from "@/features/crm/components/molecules/select-contact";
import SelectOfferings from "@/features/crm/components/molecules/select-offerings";
import SelectOrderStage from "@/features/crm/components/molecules/select-order-stage";
import SelectOrderBenefits from "@/features/crm/components/molecules/select-order-benefits";
import OrderCurrencyDisplay from "@/features/crm/components/molecules/order-currency-display";
import { useState, useEffect } from "react";
import { useCreateOrder } from "@/features/crm/hooks/use-order";
import { useNavigate } from "react-router-dom";
import { type AxiosError } from "axios";
import { openErrorNotification } from "@lib/notification";
import { extractErrorMessages } from "@lib/error-helpers";

const { Text } = Typography;

type Props = {
    handleCloseModal?: () => void;
    initialValues?: Partial<CreateOrderFormValues>;
};

export default function CreateOrderForm({ handleCloseModal, initialValues }: Props) {
    const [completeOrderInfo, setCompleteProfile] = useState<boolean>(false);
    const [addOrderForm] = Form.useForm();
    const navigate = useNavigate();

    const { notification } = App.useApp();

    useEffect(() => {
        const formValues = {
            owner: "",
            products: [],
            stage: OrderStage.PROSPECT,
            benefits: [],
            ...initialValues,
        };
        addOrderForm.setFieldsValue(formValues);
    }, [initialValues, addOrderForm]);

    const onCreateOrderSuccess = (data: Order) => {
        notification.success({
            message: "Orden creada",
            description: `La orden ${data.oid?.slice(-6)} fue creada exitosamente`,
        });
        addOrderForm.resetFields(); // Resetear formulario después de crear orden
        handleCloseModal?.();
        if (completeOrderInfo) {
            navigate(`/crm/orders/${data.oid}`);
        }
    };

    const onCreateOrderError = (error: AxiosError) => {
        const errorMessages: string[] = extractErrorMessages(
            error.response?.data || {},
        ).filter((msg) => typeof msg === "string");

        openErrorNotification(
            "Error al crear la orden",
            errorMessages.length && error.response?.status !== 500
                ? errorMessages
                : "Ocurrió un error inesperado",
            notification,
        );
    };

    const { mutate: createOrder } = useCreateOrder({
        onCreateOrderSuccess,
        onCreateOrderError: (error: AxiosError) => {
            onCreateOrderError(error);
        },
    });
    const handleOderFormOnFinish = (values: CreateOrderFormValues) => {
        createOrder({
            ...values,
        });
    };

    return (
        <Form
            name="create-order-form"
            layout="vertical"
            form={addOrderForm}
            onFinish={handleOderFormOnFinish}
            initialValues={{
                owner: "",
                products: [],
                stage: OrderStage.PROSPECT,
                benefits: [],
                ...initialValues,
            }}
        >
            <Form.Item<CreateOrderFormValues>
                name="owner"
                label={<span className="font-semibold">Contacto</span>}
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <SelectContact />
            </Form.Item>

            {/* Mostrar moneda basada en el número de teléfono del contacto */}
            <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                    prevValues.owner !== currentValues.owner
                }
            >
                {() => <OrderCurrencyDisplay />}
            </Form.Item>
            <Form.Item<CreateOrderFormValues>
                name="products"
                label={<span className="font-semibold">Productos</span>}
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <SelectOfferings mode="multiple" />
            </Form.Item>
            <Form.Item<CreateOrderFormValues>
                name="stage"
                label={<span className="font-semibold">Etapa</span>}
                rules={[
                    {
                        required: true,
                    },
                ]}
            >
                <SelectOrderStage />
            </Form.Item>
            <Form.Item<CreateOrderFormValues>
                name="benefits"
                label={<span className="font-semibold">Beneficios</span>}
            >
                <SelectOrderBenefits />
            </Form.Item>

            <Divider className="my-4" />
            <div className="flex justify-end py-2 gap-2 items-center">
                <Text type="secondary">¿Completar información de Orden?</Text>
                <Switch
                    size="small"
                    value={completeOrderInfo}
                    onChange={setCompleteProfile}
                />
            </div>
            <div className="grid grid-cols-2 gap-2 items-end">
                <Button
                    onClick={() => {
                        handleCloseModal && handleCloseModal();
                    }}
                    className="h-fit"
                    size="large"
                >
                    Cancelar
                </Button>
                <Button
                    type="primary"
                    htmlType="submit"
                    className="h-fit"
                    size="large"
                    block
                >
                    Guardar
                </Button>
            </div>
        </Form>
    );
}
