import { PaginatedResponse } from "@myTypes/base";
import {
    ContactListItem,
    ContactCreateRequest,
    ContactRetrieve,
    ContactUpdateRequest,
} from "@/features/crm/types/contact";
import { portalsApi } from "@services/portals";
import { AxiosResponse } from "axios";
import { UploadFile } from "antd";

const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE = 1;

export type ContactQueryParams = {
    page?: number;
    pageSize?: number;
    isStaff?: boolean;
    isActive?: boolean;
    search?: string;
};

export const getContacts = async (
    query: ContactQueryParams = {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
    },
): Promise<PaginatedResponse<ContactListItem>> => {
    const response = await portalsApi.get("crm/contacts", {
        params: {
            ...query,
        },
    });
    return response.data;
};

export const retrieveContact = async (cid: string): Promise<ContactRetrieve> => {
    const response = await portalsApi.get(`crm/contacts/${cid}`);
    return response.data;
};

export const createContact = async (
    contact: ContactCreateRequest,
): Promise<ContactListItem> => {
    const response = await portalsApi.post("crm/contacts", contact);
    return response.data;
};

export const updateContact = async (
    cid: string,
    contact: ContactUpdateRequest,
): Promise<ContactRetrieve> => {
    const response = await portalsApi.patch(`crm/contacts/${cid}`, contact);
    return response.data;
};

export const uploadContactProfilePhoto = async (
    cid: string,
    file: UploadFile,
): Promise<{ fid: string }> => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/contacts/${cid}/upload-profile-photo`,
        formData,
    );
    return response.data;
};

export const removeContactProfilePhoto = async (
    cid: string,
    fid: string,
): Promise<AxiosResponse> => {
    const res = await portalsApi.delete(
        `crm/contacts/${cid}/delete-profile-photo/${fid}`,
    );
    return res;
};

export const deleteContact = async (cid: string): Promise<AxiosResponse> => {
    const res = await portalsApi.delete(`crm/contacts/${cid}`);
    return res;
};

export const googleContactSync = async (cid: string): Promise<ContactRetrieve> => {
    const response = await portalsApi.patch(`crm/contacts/${cid}/sync-google`);
    return response.data;
};
