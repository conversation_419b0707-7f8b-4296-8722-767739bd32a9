import { <PERSON><PERSON>, Button, Empty, App } from "antd";
import clsx from "clsx";
import OrderCard from "../molecules/order-card";
import {
    CheckCircle,
    CreditCard,
    Heart,
    PlusCircle,
    UserPlus,
    XCircle,
    Loader2,
} from "lucide-react";
import { Order, OrderStage } from "../../types/order";
import { useEffect, useRef, useCallback } from "react";
import { makeDroppable } from "@lib/drag-and-drop";
import { useMutation } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";
import queryClient from "@lib/queryClient";
import "@lib/drag-and-drop.css";
import type { AxiosError } from "axios";
import { openErrorNotification } from "@lib/notification";
import { extractErrorMessages } from "@lib/error-helpers";

type OrdersKanbanViewProps = {
    prospectOrders: Order[];
    interestedOrders: Order[];
    toPayOrders: Order[];
    soldOrders: Order[];
    lostOrders: Order[];
    showLostColumn?: boolean;
    handleOpenCreateOrderModal?: (stage: OrderStage) => void;
};

export default function OrdersKanbanView({
    prospectOrders,
    interestedOrders,
    toPayOrders,
    soldOrders,
    lostOrders,
    showLostColumn = false,
    handleOpenCreateOrderModal,
}: OrdersKanbanViewProps) {
    const { message, notification } = App.useApp();

    // Referencias para las columnas
    const prospectColumnRef = useRef<HTMLDivElement>(null);
    const interestedColumnRef = useRef<HTMLDivElement>(null);
    const toPayColumnRef = useRef<HTMLDivElement>(null);
    const paidColumnRef = useRef<HTMLDivElement>(null);
    const lostColumnRef = useRef<HTMLDivElement>(null);

    // Mutación para actualizar órdenes
    const { mutate: updateOrderStage, isPending } = useMutation({
        mutationFn: ({ oid, stage }: { oid: string; stage: OrderStage }) =>
            partialUpdateOrder(oid, { stage }),
        onSuccess: () => {
            message.success("Orden actualizada exitosamente");
            queryClient.invalidateQueries({ queryKey: ["orders"] });
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = extractErrorMessages(
                error.response?.data || {},
            ).filter((msg) => typeof msg === "string");

            openErrorNotification(
                "Error al actualizar la orden",
                errorMessages.length
                    ? errorMessages
                    : "Ocurrió un error inesperado al crear la orden",
                notification,
            );
        },
    });

    // Función para manejar el drop
    const handleOrderDrop = useCallback(
        (order: Order, sourceStage: OrderStage, targetStage: OrderStage) => {
            if (sourceStage === targetStage) return;

            updateOrderStage({ oid: order.oid, stage: targetStage });
        },
        [updateOrderStage],
    );

    // Setup drag and drop para las columnas
    useEffect(() => {
        const cleanupFunctions: (() => void)[] = [];

        if (prospectColumnRef.current) {
            cleanupFunctions.push(
                makeDroppable(
                    prospectColumnRef.current,
                    OrderStage.PROSPECT,
                    handleOrderDrop,
                ),
            );
        }
        if (interestedColumnRef.current) {
            cleanupFunctions.push(
                makeDroppable(
                    interestedColumnRef.current,
                    OrderStage.INTERESTED,
                    handleOrderDrop,
                ),
            );
        }
        if (toPayColumnRef.current) {
            cleanupFunctions.push(
                makeDroppable(
                    toPayColumnRef.current,
                    OrderStage.TO_PAY,
                    handleOrderDrop,
                ),
            );
        }
        if (paidColumnRef.current) {
            cleanupFunctions.push(
                makeDroppable(paidColumnRef.current, OrderStage.SOLD, handleOrderDrop),
            );
        }
        if (showLostColumn && lostColumnRef.current) {
            cleanupFunctions.push(
                makeDroppable(lostColumnRef.current, OrderStage.LOST, handleOrderDrop),
            );
        }

        return () => {
            cleanupFunctions.forEach((cleanup) => cleanup());
        };
    }, [showLostColumn, handleOrderDrop]);

    const handleAddProspect = () => {
        handleOpenCreateOrderModal?.(OrderStage.PROSPECT);
    };

    const handleAddInterested = () => {
        handleOpenCreateOrderModal?.(OrderStage.INTERESTED);
    };

    const handleAddToPay = () => {
        handleOpenCreateOrderModal?.(OrderStage.TO_PAY);
    };

    const handleAddPaid = () => {
        handleOpenCreateOrderModal?.(OrderStage.SOLD);
    };

    const handleAddLost = () => {
        handleOpenCreateOrderModal?.(OrderStage.LOST);
    };

    return (
        <div
            className={clsx("gap-4 grid grid-cols-1", {
                "lg:grid-cols-5": showLostColumn,
                "lg:grid-cols-4": !showLostColumn,
            })}
        >
            <div
                ref={prospectColumnRef}
                className={clsx(
                    "col-span-1 bg-white rounded-lg shadow-sm p-4 bg-white-full border-t-4 border-blue-500 transition-all duration-200 hover:shadow-md",
                    "min-h-[400px] relative",
                    { "opacity-50 pointer-events-none": isPending },
                )}
            >
                <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                        <p className="font-semibold text-blue-600">Prospectos</p>
                        <Badge
                            count={prospectOrders.length}
                            className="ml-2"
                            style={{ backgroundColor: "#1890ff" }}
                        />
                    </div>
                    <button
                        className="text-blue-500 hover:text-blue-700 transition-colors"
                        onClick={handleAddProspect}
                    >
                        <PlusCircle size={18} />
                    </button>
                </div>

                {prospectOrders.length > 0 ? (
                    prospectOrders.map((order) => (
                        <OrderCard key={order.oid} order={order} />
                    ))
                ) : (
                    <Empty
                        description="No hay prospectos"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                )}

                {isPending && (
                    <div className="loading-overlay">
                        <Loader2 size={20} className="animate-spin text-blue-600" />
                    </div>
                )}

                <div className="mt-3 flex justify-center">
                    <Button
                        type="primary"
                        size="small"
                        icon={<UserPlus size={14} />}
                        onClick={handleAddProspect}
                    >
                        Agregar prospecto
                    </Button>
                </div>
            </div>

            <div
                ref={interestedColumnRef}
                className={clsx(
                    "col-span-1 bg-white rounded-lg shadow-sm p-4 bg-white-full border-t-4 border-green-500 transition-all duration-200 hover:shadow-md",
                    "min-h-[400px] relative",
                    { "opacity-50 pointer-events-none": isPending },
                )}
            >
                <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                        <p className="font-semibold text-green-600">Interesados</p>
                        <Badge
                            count={interestedOrders.length}
                            className="ml-2"
                            style={{ backgroundColor: "#10b981" }}
                        />
                    </div>
                    <button
                        className="text-green-500 hover:text-green-700 transition-colors"
                        onClick={handleAddInterested}
                    >
                        <PlusCircle size={18} />
                    </button>
                </div>

                {interestedOrders.length > 0 ? (
                    interestedOrders.map((order) => (
                        <OrderCard key={order.oid} order={order} />
                    ))
                ) : (
                    <Empty
                        description="No hay interesados"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                )}

                {isPending && (
                    <div className="loading-overlay">
                        <Loader2 size={20} className="animate-spin text-green-600" />
                    </div>
                )}

                <div className="mt-3 flex justify-center">
                    <Button
                        type="primary"
                        size="small"
                        style={{ backgroundColor: "#10b981" }}
                        icon={<Heart size={14} />}
                        onClick={handleAddInterested}
                    >
                        Añadir interesado
                    </Button>
                </div>
            </div>

            <div
                ref={toPayColumnRef}
                className={clsx(
                    "col-span-1 bg-white rounded-lg shadow-sm p-4 bg-white-full border-t-4 border-yellow-500 transition-all duration-200 hover:shadow-md",
                    "min-h-[400px] relative",
                    { "opacity-50 pointer-events-none": isPending },
                )}
            >
                <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                        <p className="font-semibold text-yellow-600">Por pagar</p>
                        <Badge
                            count={toPayOrders.length}
                            className="ml-2"
                            style={{ backgroundColor: "#f59e0b" }}
                        />
                    </div>
                    <button
                        className="text-yellow-500 hover:text-yellow-700 transition-colors"
                        onClick={handleAddToPay}
                    >
                        <PlusCircle size={18} />
                    </button>
                </div>

                {toPayOrders.length > 0 ? (
                    toPayOrders.map((order) => (
                        <OrderCard key={order.oid} order={order} />
                    ))
                ) : (
                    <Empty
                        description="No hay órdenes por pagar"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                )}

                {isPending && (
                    <div className="loading-overlay">
                        <Loader2 size={20} className="animate-spin text-yellow-600" />
                    </div>
                )}

                <div className="mt-3 flex justify-center">
                    <Button
                        type="primary"
                        size="small"
                        style={{ backgroundColor: "#f59e0b" }}
                        icon={<CreditCard size={14} />}
                        onClick={handleAddToPay}
                    >
                        Añadir pendiente
                    </Button>
                </div>
            </div>

            <div
                ref={paidColumnRef}
                className={clsx(
                    "col-span-1 bg-white rounded-lg shadow-sm p-4 bg-white-full border-t-4 border-purple-500 transition-all duration-200 hover:shadow-md",
                    "min-h-[400px] relative",
                    { "opacity-50 pointer-events-none": isPending },
                )}
            >
                <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
                        <p className="font-semibold text-purple-600">Pagados</p>
                        <Badge
                            count={soldOrders.length}
                            className="ml-2"
                            style={{ backgroundColor: "#8b5cf6" }}
                        />
                    </div>
                    <button
                        className="text-purple-500 hover:text-purple-700 transition-colors"
                        onClick={handleAddPaid}
                    >
                        <PlusCircle size={18} />
                    </button>
                </div>

                {soldOrders.length > 0 ? (
                    soldOrders.map((order) => (
                        <OrderCard key={order.oid} order={order} />
                    ))
                ) : (
                    <Empty
                        description="No hay órdenes pagadas"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                )}

                {isPending && (
                    <div className="loading-overlay">
                        <Loader2 size={20} className="animate-spin text-purple-600" />
                    </div>
                )}

                <div className="mt-3 flex justify-center">
                    <Button
                        type="primary"
                        size="small"
                        style={{ backgroundColor: "#8b5cf6" }}
                        icon={<CheckCircle size={14} />}
                        onClick={handleAddPaid}
                    >
                        Añadir pagado
                    </Button>
                </div>
            </div>

            {showLostColumn && (
                <div
                    ref={lostColumnRef}
                    className={clsx(
                        "col-span-1 bg-white rounded-lg shadow-sm p-4 bg-white-full border-t-4 border-red-500 transition-all duration-200 hover:shadow-md",
                        "min-h-[400px] relative",
                        { "opacity-50 pointer-events-none": isPending },
                    )}
                >
                    <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                            <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <p className="font-semibold text-red-600">Perdido</p>
                            <Badge
                                count={lostOrders.length}
                                className="ml-2"
                                style={{ backgroundColor: "#ef4444" }}
                            />
                        </div>
                        <button
                            className="text-red-500 hover:text-red-700 transition-colors"
                            onClick={handleAddLost}
                        >
                            <PlusCircle size={18} />
                        </button>
                    </div>

                    {lostOrders.length > 0 ? (
                        lostOrders.map((order) => (
                            <OrderCard key={order.oid} order={order} />
                        ))
                    ) : (
                        <Empty
                            description="No hay órdenes perdidas"
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                        />
                    )}

                    {isPending && (
                        <div className="loading-overlay">
                            <Loader2 size={20} className="animate-spin text-red-600" />
                        </div>
                    )}

                    <div className="mt-3 flex justify-center">
                        <Button
                            type="primary"
                            size="small"
                            danger
                            icon={<XCircle size={14} />}
                            onClick={handleAddLost}
                        >
                            Añadir perdido
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}
