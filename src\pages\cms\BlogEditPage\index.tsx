import { useParams } from "react-router-dom";
import BlogEditorLayout from "@layouts/cms/BlogEditorLayout";
import { Input, Form } from "antd";
import { useDebounce } from "@hooks/use-debounce";
import { useEffect, useState, useCallback, useRef } from "react";
import { SavePostStatusEnum, useSavePostStore } from "@store/savePostStore";
import RichtextEditor from "@components/cms/organisms/RichTextEditor";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { retrieveBlogPost, updateBlogPost } from "@services/portals/cms/blogs/post";
import { BlogPost, UpdateBlogPostBody } from "@myTypes/blog";
import Spinner from "@components/shared/atoms/Spinner";
import UploadBlogImages from "../../../components/cms/organisms/BlogEdit/UploadBlogImages";

const { TextArea } = Input;

export default function BlogEditPage() {
    const { bid } = useParams<{ bid: string }>();
    const { save, saveStatus } = useSavePostStore();
    const queryClient = useQueryClient();
    const [form] = Form.useForm();

    // Referencias para controlar el estado de inicialización y cambios del usuario
    const isInitializedRef = useRef(false);
    const userChangedTitleRef = useRef(false);
    const isSavingRef = useRef(false);

    // Estado para almacenar los valores originales para comparación
    const [originalState, setOriginalState] = useState<
        Partial<Pick<BlogPost, "bid" | "title">>
    >({});

    // Aplicar debounce al título
    const titleValue = Form.useWatch("title", form);
    const debouncedTitle = useDebounce(titleValue, 1500);

    // Consulta para obtener los datos del blog
    const {
        data: blog,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ["blog", bid],
        queryFn: () => retrieveBlogPost(bid),
        enabled: !!bid,
    });

    // Solo para asegurarnos de que los datos están actualizados al navegar entre blogs
    useEffect(() => {
        refetch();
    }, [refetch, bid]);

    // Mutación para actualizar el blog
    const { mutateAsync: updatePost } = useMutation({
        mutationKey: ["updateBlog", bid],
        mutationFn: (blog: UpdateBlogPostBody) => updateBlogPost(bid, blog),
        onSuccess: (blog) => {
            // Actualizar estado original después de un guardado exitoso
            const currentTitle = form.getFieldValue("title");

            setOriginalState({
                title: currentTitle,
                bid,
            });

            // Invalidar título en sidebar
            queryClient.invalidateQueries({
                queryKey: ["blogs", blog.status],
            });

            // Actualizar flag de guardado
            isSavingRef.current = false;
        },
        onError: () => {
            isSavingRef.current = false;
        },
    });

    // Mutación para actualizar las imágenes del blog
    const updateBlogMutation = useMutation({
        mutationFn: (data: UpdateBlogPostBody) => updateBlogPost(bid, data),
        onSuccess: () => {
            refetch();
        },
    });

    const hasTitleChanges = useCallback(() => {
        if (!debouncedTitle) return false;
        return debouncedTitle !== originalState.title;
    }, [debouncedTitle, originalState.title]);

    const handleTitleChange = useCallback(() => {
        userChangedTitleRef.current = true;
    }, []);

    const saveChanges = useCallback(async () => {
        if (isSavingRef.current || saveStatus === SavePostStatusEnum.SAVING) return;

        try {
            await form.validateFields();
            const formData = form.getFieldsValue();
            isSavingRef.current = true;

            await save(() =>
                updatePost({
                    ...formData,
                }),
            );
        } catch (error) {
            console.error("Error al validar formulario:", error);
            isSavingRef.current = false;
        }
    }, [form, save, updatePost, saveStatus]);

    /**
     * Efecto para cargar datos iniciales cuando cambia el blog
     */
    useEffect(() => {
        if (blog) {
            userChangedTitleRef.current = false;
            form.setFieldsValue({ title: blog.title });
            setOriginalState({
                title: blog.title,
                bid,
            });

            isInitializedRef.current = true;
        }
    }, [blog, form, bid]);

    /**
     * Efecto para guardar automáticamente cuando cambia el título
     */
    useEffect(() => {
        // Solo proceder si está inicializado y el usuario ha cambiado el título
        if (!isInitializedRef.current || !userChangedTitleRef.current) return;

        // Verificar si hay cambios en el título
        if (hasTitleChanges()) {
            saveChanges();
        }
    }, [debouncedTitle, hasTitleChanges, saveChanges]);

    return (
        <BlogEditorLayout>
            {isLoading && (
                <div className="min-h-[calc(100vh-112px)] grid place-content-center">
                    <Spinner />
                </div>
            )}
            {blog && (
                <div className="flex flex-col w-full">
                    <Form
                        form={form}
                        layout="vertical"
                        initialValues={{ title: blog?.title }}
                    >
                        <div>
                            <UploadBlogImages
                                blogId={blog.bid}
                                description={blog.title}
                                coverImage={blog.coverImage}
                                thumbnail={blog.thumbnail}
                                onImageUpdate={(type, imageId) => {
                                    const updateData: UpdateBlogPostBody = {};

                                    if (type === "cover") {
                                        updateData.coverImageId = imageId;
                                    } else {
                                        updateData.thumbnailId = imageId;
                                    }

                                    updateBlogMutation.mutate(updateData);
                                }}
                            />
                        </div>
                        <Form.Item
                            name="title"
                            rules={[
                                {
                                    required: true,
                                    message: "El título es obligatorio",
                                },
                            ]}
                        >
                            <TextArea
                                placeholder="Título"
                                autoSize
                                className="text-3xl font-bold p-0 resize-none border-none shadow-none focus:shadow-none"
                                onChange={handleTitleChange}
                            />
                        </Form.Item>
                    </Form>

                    <div className="relative">
                        <RichtextEditor
                            blogId={blog.bid}
                            placeholder="Escriba aquí, / para ver los comandos disponibles ..."
                            initialContent={blog.content}
                        />
                    </div>
                </div>
            )}
        </BlogEditorLayout>
    );
}
