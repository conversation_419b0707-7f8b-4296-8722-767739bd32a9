import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export enum UserRole {
    STAFF = "STAFF",
    SUPER_USER = "SUPERUSER",
}

type AuthUser = {
    uid: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
};

export type AuthState = {
    key?: string;
    user?: AuthUser;
    isAuthenticated?: boolean;

    isHydrated: boolean;
};

type AuthActions = {
    setToken: (token: string) => void;
    login: ({
        key,
        user,
        isAuthenticated,
    }: {
        key: string;
        user: AuthUser;
        isAuthenticated: boolean;
    }) => void;
    logout: () => void;
    setIsHydrated: (isHydrated: boolean) => void;
};

type AuthStore = AuthState & AuthActions;

const initialState: AuthState = {
    key: undefined,
    user: undefined,
    isAuthenticated: false,
    isHydrated: false,
};

export const useAuthStore = create<AuthStore>()(
    persist(
        (set) => ({
            ...initialState,
            setToken: (token) => {
                set((state) => ({ ...state, key: token }));
            },
            setIsHydrated: (isHydrated) => {
                set((state) => ({ ...state, isHydrated }));
            },
            login: ({ key, user, isAuthenticated }) => {
                set((state) => ({ ...state, key, user, isAuthenticated }));
            },
            logout: () => {
                set((state) => ({
                    ...state,
                    key: undefined,
                    user: undefined,
                    isAuthenticated: false,
                }));
            },
        }),
        {
            name: "authStore",
            storage: createJSONStorage(() => localStorage),
            onRehydrateStorage: () => (state) => {
                state?.setIsHydrated(true);
            },
        },
    ),
);
