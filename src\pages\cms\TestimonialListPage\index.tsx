import { useEffect, useMemo, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Empty,
    Form,
    Image,
    Input,
    message,
    Modal,
    notification,
    Pagination,
    Popover,
    Table,
    Tag,
    Typography,
    Upload,
} from "antd";
import type { TableProps, UploadFile, UploadProps } from "antd";

const { Text } = Typography;
const { Search } = Input;
const { Dragger } = Upload;

import Trash from "@assets/icons/huge/trash-white.svg?react";
import Plus from "@assets/icons/general/plus-white.svg?react";
import Reload from "@assets/icons/huge/reload.svg?react";
import Import from "@assets/icons/huge/import.svg?react";
import Settings from "@assets/icons/huge/settings.svg?react";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";
import EditStroke from "@assets/icons/huge/edit-stroke.svg?react";
import MoreVertical from "@assets/icons/huge/more-vertical.svg?react";

import CloudUpload from "@assets/shapes/cloud-upload.svg?react";

import {
    listTestimonials,
    createTestimonial,
    deleteTestimonial,
} from "@services/portals/cms/testimonial";
import Spinner from "@components/shared/atoms/Spinner";
import { formatDateTime, getBase64 } from "@lib/helpers";
import ImgCrop from "antd-img-crop";
import { onSuccessMessage } from "@lib/message";
import { openErrorNotification } from "@lib/notification";
import { AxiosError } from "axios";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { CreateTestimonial, Testimonial } from "@myTypes/testimonial";
import CmsLayout from "@layouts/cms/CmsLayout";

// type FileType = Parameters<GetProp<UploadProps, "beforeUpload">>[0];

type draggerProps = {
    customRequest: UploadProps["customRequest"];
    onRemove?: UploadProps["onRemove"];
    onPreview?: UploadProps["onPreview"];
};

const INITIAL_COLUMNS: TableProps<Testimonial>["columns"] = [
    {
        title: "AUTOR",
        dataIndex: "authorName",
        key: "authorName",
        render: (authorName: string, record: Testimonial) => (
            <Link
                to={`${record.tid}`}
                className="text-blue-full font-semibold underline"
            >
                {authorName}
            </Link>
        ),
    },
    {
        title: "TITULO",
        dataIndex: "authorTitle",
        key: "authorTitle",
        render: (authorTitle: string | undefined) => (
            <Text>{authorTitle ? authorTitle : "-"}</Text>
        ),
    },
    {
        title: "ESTADO",
        dataIndex: "status",
        key: "status",
        render: (status: string) => (
            <Tag
                bordered={false}
                color={status === "Published" ? "green" : "volcano"}
                className="rounded-full px-3"
            >
                {status === "Published" ? "Publicado" : "No publicado"}
            </Tag>
        ),
    },
    {
        title: "ORDEN",
        dataIndex: "order",
        key: "order",
        render: (order: number | null) => <Text>{order !== null ? order : "-"}</Text>,
    },
    {
        title: "ÚLTIMA ACTUALIZACIÓN",
        dataIndex: "updatedAt",
        key: "updatedAt",
        render: (updatedAt: string) => {
            const formattedDate = formatDateTime(updatedAt);
            return <Text>{formattedDate}</Text>;
        },
    },
    {
        title: "FECHA DE CREACIÓN",
        dataIndex: "createdAt",
        key: "createdAt",
        render: (createdAt: string) => {
            const formattedDate = formatDateTime(createdAt);
            return <Text>{formattedDate}</Text>;
        },
    },
];

const PAGE_SIZE = 10;

const INITIAL_CHECKED_VALUES = [
    "authorName",
    "authorTitle",
    "status",
    "order",
    "createdAt",
];

const COLUMN_OPTIONS = [
    {
        label: "Autor",
        value: "authorName",
    },
    {
        label: "Título",
        value: "authorTitle",
    },
    {
        label: "Estado",
        value: "status",
    },
    {
        label: "Orden",
        value: "order",
    },
    {
        label: "Fecha de Actualización",
        value: "updatedAt",
    },
    {
        label: "Fecha de Creación",
        value: "createdAt",
    },
];

const AUTHOR_PHOTO_WIDTH = 120;
const AUTHOR_PHOTO_HEIGHT = 120;

export default function TestimonialListPage() {
    // react-router-dom hooks
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    const DEFAULT_PAGE = searchParams.get("page")
        ? Number(searchParams.get("page"))
        : 1;

    // AndtD hooks
    const [messageApi, messageContextHolder] = message.useMessage();
    const [notificationApi, notificationContextHolder] = notification.useNotification();
    const [addForm] = Form.useForm<CreateTestimonial>();

    // Page States
    const [modalOpen, setModalOpen] = useState(false);
    const [checkedValues, setCheckedValues] =
        useState<string[]>(INITIAL_CHECKED_VALUES);
    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [previewImage, setPreviewImage] = useState<string | undefined>(undefined);
    const [previewOpen, setPreviewOpen] = useState(false);

    // React Query hooks
    const {
        isPending,
        data,
        isError,
        error: _error,
        refetch,
    } = useQuery({
        queryKey: ["testimonials", currentPage],
        queryFn: () => listTestimonials(currentPage),
    });

    const deleteMutation = useMutation({
        mutationFn: (tid: string) => deleteTestimonial(tid),
        onSuccess: () => {
            onSuccessMessage("Testimonio eliminado correctamente", messageApi);
            refetch();
        },
    });

    const createMutation = useMutation({
        mutationFn: (data: CreateTestimonial) => createTestimonial(data),
        onSuccess: () => {
            onSuccessMessage("Testimonio creado correctamente", messageApi);
            setModalOpen(false);
            addForm.resetFields();
            refetch();
        },
        onError: (error: AxiosError) => {
            // TODO: Handle error
            console.error("Error: ", error);
        },
    });

    const testimonials = data?.results;
    const TOTAL_COUNT = data?.count;
    const tableColumns = useMemo(() => {
        return (INITIAL_COLUMNS ?? []).filter((column) =>
            checkedValues.includes(column.key as string),
        );
    }, [checkedValues]);

    const handleShowHideColumns = (checkedValues: string[]) => {
        setCheckedValues(checkedValues);
    };

    useEffect(() => {
        if (isError) {
            const error = _error as AxiosError;
            openErrorNotification(
                "Error al cargar los testimonios",
                error.message,
                notificationApi,
            );
        }
    }, [isError, _error, notificationApi]);

    const defaultColumn = {
        title: "ACCIONES",
        key: "actions",
        render: (record: Testimonial) => (
            <Dropdown
                trigger={["click"]}
                menu={{
                    items: [
                        {
                            key: "edit",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-blue-full">
                                        <EditStroke className="w-5 h-5" /> Editar
                                    </div>
                                </>
                            ),
                        },
                        {
                            key: "delete",
                            label: (
                                <>
                                    <div className="flex items-center gap-2 text-state-red-full">
                                        <DeleteStroke className="w-5 h-5" /> Eliminar
                                    </div>
                                </>
                            ),
                        },
                    ],
                    onClick: ({ key }) => {
                        handleRowAction(key, record);
                    },
                }}
                placement="bottomRight"
            >
                <Button
                    icon={<MoreVertical className="w-5 h-5" />}
                    type="text"
                    size="small"
                />
            </Dropdown>
        ),
    };

    const handleRowAction = (key: string, record: Testimonial) => {
        if (key === "edit") {
            navigate(`/cms/testimonial/${record.tid}`);
        } else if (key === "delete") {
            const testimonialId = record.tid;
            deleteMutation.mutate(testimonialId);
        }
    };

    const handlePageChange = (newPage: number) => {
        setSearchParams({ page: newPage.toString() });
        setCurrentPage(newPage);
    };

    const handleReloadData = () => {
        refetch();
    };

    const handleFormFinish = async (values: CreateTestimonial) => {
        createMutation.mutate(values);
    };

    const draggerProps: draggerProps = {
        customRequest: async (options) => {
            const { file, onSuccess, onError } = options;
            try {
                typeof onSuccess === "function" && onSuccess(file);
                const uploadFile = file as UploadFile;
                const base64file = await getBase64(file as File);
                uploadFile.url = base64file;
                addForm.setFieldsValue({
                    authorPhotoFile: [uploadFile],
                });
                setPreviewImage(base64file);
            } catch (error) {
                typeof onError === "function" && onError(error as Error);
            }
        },
        onRemove: () => {
            addForm.setFieldsValue({
                authorPhotoFile: [],
            });
        },
        onPreview: () => {
            setPreviewOpen(true);
        },
    };

    return (
        <>
            {notificationContextHolder}
            {messageContextHolder}
            <CmsLayout>
                <div className="w-full h-full space-y-5">
                    <Modal
                        centered
                        open={modalOpen}
                        title={
                            <div className="w-full flex justify-center text-2xl py-4">
                                Agregar nuevo Testimonio
                            </div>
                        }
                        footer={false}
                        onCancel={() => setModalOpen(false)}
                    >
                        <Form
                            name="testimonial"
                            layout="vertical"
                            onFinish={handleFormFinish}
                            form={addForm}
                        >
                            <Form.Item<CreateTestimonial>
                                name="authorName"
                                label={
                                    <span className="font-semibold text-base">
                                        Nombres y Apellidos
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: "Por favor, ingrese los nombres",
                                    },
                                ]}
                            >
                                <Input placeholder="Nombres" className="py-2" />
                            </Form.Item>
                            <Form.Item<CreateTestimonial>
                                name="authorTitle"
                                label={
                                    <span className="font-semibold text-base">Rol</span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: "Por favor, ingrese el rol o título",
                                    },
                                ]}
                            >
                                <Input placeholder="Rol" className="py-2" />
                            </Form.Item>
                            <Form.Item<CreateTestimonial>
                                name="content"
                                label={
                                    <span className="font-semibold text-base">
                                        Cita
                                    </span>
                                }
                                rules={[
                                    {
                                        required: true,
                                        message: "Por favor, ingrese la cita",
                                    },
                                ]}
                            >
                                <Input.TextArea placeholder="Cita" className="py-2" />
                            </Form.Item>
                            <Form.Item<CreateTestimonial>
                                name="authorPhotoFile"
                                label={
                                    <span className="font-semibold text-base">
                                        Imagen
                                    </span>
                                }
                                valuePropName="fileList"
                                getValueFromEvent={(e) => {
                                    if (Array.isArray(e)) {
                                        return e;
                                    }
                                    return e && e.fileList;
                                }}
                            >
                                <ImgCrop
                                    aspect={AUTHOR_PHOTO_WIDTH / AUTHOR_PHOTO_HEIGHT}
                                    showGrid={true}
                                    modalTitle="Cargar Imagen"
                                    modalOk="Guardar"
                                    modalCancel="Cancelar"
                                >
                                    <Dragger
                                        listType="picture"
                                        maxCount={1}
                                        multiple={false}
                                        onChange={(info) => {
                                            console.info("change info: ", info); // TODO: Implement change
                                        }}
                                        {...draggerProps}
                                    >
                                        <div className="flex flex-col justify-center items-center">
                                            <CloudUpload />
                                            <Text className="font-medium text-black-full">
                                                Arrastre una imagen de perfil o haga
                                                click aquí
                                            </Text>
                                            <Text className="text-xs text-black-medium">
                                                Este campo admite formatos de imagen.
                                                Solo una imagen
                                            </Text>
                                        </div>
                                    </Dragger>
                                </ImgCrop>
                            </Form.Item>
                            <Image
                                wrapperStyle={{ display: "none" }}
                                preview={{
                                    visible: previewOpen,
                                    onVisibleChange: setPreviewOpen,
                                }}
                                src={previewImage}
                            />
                            <div className="grid grid-cols-2 gap-2">
                                <Button
                                    onClick={() => setModalOpen(false)}
                                    className="h-fit"
                                    size="large"
                                >
                                    Cancelar
                                </Button>
                                <Form.Item>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        className="h-fit"
                                        size="large"
                                        block
                                    >
                                        Guardar
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </Modal>
                    <div className="flex justify-between items-center">
                        <WelcomeBar helperText="Gestiona aquí los Testimonios" />
                        <div className="flex gap-3">
                            <Button
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Import />}
                            >
                                Importar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Plus />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </div>
                    <div className="p-5 bg-white-full rounded-lg space-y-5">
                        <div className="flex justify-between items-center">
                            <Text className="text-black-medium text-2xl font-semibold">
                                Testimonios
                            </Text>

                            <div className="flex items-center gap-3">
                                <Search
                                    size="large"
                                    enterButton
                                    allowClear
                                    onSearch={(value, _, info) => {
                                        console.info(info?.source, value); // TODO: Implement search
                                    }}
                                />
                                <Button
                                    icon={<Reload />}
                                    size="large"
                                    type="text"
                                    onClick={handleReloadData}
                                />
                                <Popover
                                    content={
                                        <div className="p-2 space-y-3">
                                            <div className="uppercase text-black-medium font-medium">
                                                Mostrar/Ocultar Columnas
                                            </div>
                                            <div className="px-2">
                                                <Checkbox.Group
                                                    defaultValue={
                                                        INITIAL_CHECKED_VALUES
                                                    }
                                                    onChange={handleShowHideColumns}
                                                    name="columns"
                                                    className="flex flex-col gap-1"
                                                    options={COLUMN_OPTIONS}
                                                />
                                            </div>
                                        </div>
                                    }
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <Button
                                        icon={<Settings />}
                                        size="large"
                                        type="text"
                                    />
                                </Popover>
                            </div>
                        </div>
                        <ConfigProvider
                            theme={{
                                components: {
                                    Table: {
                                        headerBg: "#FBFCFD",
                                        borderColor: "#fff",
                                        headerSplitColor: "#fafafa",
                                        headerBorderRadius: 8,
                                        rowHoverBg: "#F6FAFD",
                                        rowSelectedBg: "#F6FAFD",
                                        rowSelectedHoverBg: "#F6FAFD",
                                        footerBg: "#F1F1F1",
                                    },
                                    Pagination: {
                                        itemSize: 36,
                                        itemActiveBg: "#F6FAFD",
                                    },
                                },
                            }}
                        >
                            <Table
                                rowSelection={{
                                    type: "checkbox",
                                }}
                                locale={{
                                    emptyText: (
                                        <>{isPending ? <Spinner /> : <Empty />}</>
                                    ),
                                }}
                                columns={
                                    tableColumns ? [...tableColumns, defaultColumn] : []
                                }
                                dataSource={testimonials}
                                className="rounded-lg"
                                footer={() => ""}
                                pagination={false}
                            />
                            <div className="flex justify-between">
                                <div className="flex items-center gap-3">
                                    <Button
                                        danger
                                        type="primary"
                                        size="large"
                                        icon={<Trash />}
                                    >
                                        Eliminar
                                    </Button>
                                </div>
                                <div>
                                    <Pagination
                                        defaultCurrent={DEFAULT_PAGE}
                                        total={TOTAL_COUNT}
                                        pageSize={PAGE_SIZE}
                                        onChange={handlePageChange}
                                    />
                                </div>
                            </div>
                        </ConfigProvider>
                    </div>
                </div>
            </CmsLayout>
        </>
    );
}
