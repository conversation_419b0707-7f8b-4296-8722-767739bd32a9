import { useMemo } from "react";
import { <PERSON><PERSON>, DatePicker, Form, Radio, Select, Switch } from "antd";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import { CreateEventScheduleFormValues } from "@/features/crm/types/event-schedule";
import { useCreateEventSchedule } from "@/features/crm/hooks/use-event-schedule";
import { useEvents } from "@/features/crm/hooks/use-event";
import { Event, EventStage, EventStageLabels } from "@/features/crm/types/event";
import dayjs, { Dayjs } from "dayjs";
import SelectPartnership from "../molecules/select-partnership";
import queryClient from "@lib/queryClient";
import { useNavigate } from "react-router-dom";

type CreateEventScheduleFormProps = {
    initialValues?: Partial<CreateEventScheduleFormValues>;
    closeModal?: () => void;
};

export default function CreateEventScheduleForm({
    closeModal,
    initialValues,
}: CreateEventScheduleFormProps) {
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const { mutate: createEventSchedule, isPending } = useCreateEventSchedule({
        onSuccess: () => {
            form.resetFields();
            closeModal?.();
            queryClient.invalidateQueries({ queryKey: ["event-schedules"] });
        },
    });

    const { events } = useEvents();

    const eventOptions = useMemo(
        () =>
            events.map((event: Event) => ({
                value: event.eid,
                label: event.name,
            })),
        [events],
    );

    const stageOptions = useMemo(
        () =>
            Object.values(EventStage).map((stage) => ({
                value: stage,
                label: EventStageLabels[stage],
            })),
        [],
    );

    const handleFormFinish = (values: CreateEventScheduleFormValues) => {
        const { completeInfo, ...payload } = values;

        createEventSchedule(
            {
                ...payload,
                startDate: dayjs(values.startDate).toISOString(),
                endDate: dayjs(values.endDate).toISOString(),
            },
            {
                onSuccess: (newEventSchedule) => {
                    if (completeInfo) {
                        navigate(`/crm/event-schedule/${newEventSchedule.esid}`);
                    }
                },
            },
        );
    };

    const handleAddOneHourToEndDate = (startDate: Dayjs) => {
        form.setFieldsValue({
            endDate: startDate.add(1, "hour"),
        });
    };

    return (
        <Form
            form={form}
            name="create-event-schedule-form"
            layout="vertical"
            onFinish={handleFormFinish}
            className="space-y-4"
            initialValues={{
                startDate: dayjs(),
                endDate: dayjs().add(1, "hour"),
                stage: EventStage.PLANNING,
                isGeneral: false,
                ...initialValues,
            }}
        >
            <Form.Item<CreateEventScheduleFormValues>
                name="event"
                label={<FormLabel>Evento</FormLabel>}
                rules={[{ required: true, message: "Por favor selecciona un evento" }]}
            >
                <Select placeholder="Selecciona un evento" options={eventOptions} />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="startDate"
                label={<FormLabel>Fecha de inicio</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "Por favor selecciona una fecha de inicio",
                    },
                ]}
            >
                <DatePicker
                    showTime
                    format="DD/MM/YYYY HH:mm"
                    placeholder="Selecciona fecha y hora de inicio"
                    className="w-full"
                    onChange={(value) => {
                        handleAddOneHourToEndDate(value);
                    }}
                />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="endDate"
                label={<FormLabel>Fecha de fin</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "Por favor selecciona una fecha de fin",
                    },
                ]}
            >
                <DatePicker
                    showTime
                    format="DD/MM/YYYY HH:mm"
                    placeholder="Selecciona fecha y hora de fin"
                    className="w-full"
                />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="stage"
                label={<FormLabel>Estado</FormLabel>}
                rules={[
                    {
                        required: true,
                        message: "Debe iniciar el evento en un estado",
                    },
                ]}
            >
                <Radio.Group
                    block
                    optionType="button"
                    buttonStyle="solid"
                    options={stageOptions}
                />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="isGeneral"
                label={<FormLabel>Tipo de evento</FormLabel>}
            >
                <Radio.Group
                    optionType="button"
                    buttonStyle="solid"
                    options={[
                        { value: false, label: "Específico" },
                        { value: true, label: "General" },
                    ]}
                />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="partnerships"
                label={<FormLabel>Alianzas</FormLabel>}
            >
                <SelectPartnership />
            </Form.Item>

            <Form.Item<CreateEventScheduleFormValues>
                name="completeInfo"
                layout="horizontal"
                tooltip="Si se activa esta opción, se le redirigirá a la vista de edición."
                label={
                    <FormLabel className="text-xs">¿Completar información?</FormLabel>
                }
            >
                <Switch size="small" />
            </Form.Item>

            <div className="flex justify-end gap-3">
                <Button onClick={closeModal}>Cancelar</Button>
                <Button type="primary" htmlType="submit" loading={isPending}>
                    Crear
                </Button>
            </div>
        </Form>
    );
}
