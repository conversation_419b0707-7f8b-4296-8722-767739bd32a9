import { useMutation, useQueryClient } from "@tanstack/react-query";
import { App, Modal } from "antd";
import { updatePayment } from "../services/portals/payment";
import { AlertTriangle } from "lucide-react";
import { type AxiosError } from "axios";
import { extractErrorMessages } from "@lib/error-helpers";
import { openErrorNotification } from "@lib/notification";

export const useTogglePaymentStatus = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();
    const { confirm } = Modal;

    const toggleMutation = useMutation({
        mutationFn: ({ pid, isPaid }: { pid: string; isPaid: boolean }) =>
            updatePayment(pid, { isPaid, paymentDate: new Date().toISOString() }),
        onSuccess: (_, variables) => {
            notification.success({
                message: variables.isPaid
                    ? "Pago marcado como pagado"
                    : "Pago marcado como pendiente",
                description: "El estado del pago se ha actualizado correctamente",
                duration: 3,
            });
            // Invalidate both the specific payment and the payments list
            queryClient.invalidateQueries({ queryKey: ["payments"] });
            queryClient.invalidateQueries({ queryKey: ["payment"] });
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = extractErrorMessages(
                error.response?.data || {},
            ).filter((msg) => typeof msg === "string");

            openErrorNotification(
                "Error al actualizar el estado",
                errorMessages.length
                    ? errorMessages
                    : "Ocurrió un error inesperado al actualizar el estado",
                notification,
            );
        },
    });

    const confirmToggle = (
        pid: string,
        currentStatus: boolean,
        hasVoucher: boolean,
    ) => {
        const newStatus = !currentStatus;

        // If marking as paid but no voucher exists, show warning
        if (newStatus && !hasVoucher) {
            confirm({
                title: "¿Marcar como pagado sin voucher?",
                icon: <AlertTriangle className="text-orange-500" />,
                content:
                    "No hay un voucher subido para este pago. ¿Estás seguro de marcarlo como pagado?",
                okText: "Sí, marcar como pagado",
                okType: "primary",
                cancelText: "Cancelar",
                onOk() {
                    toggleMutation.mutate({ pid, isPaid: newStatus });
                },
            });
        } else {
            // Normal confirmation
            confirm({
                title: newStatus ? "¿Marcar como pagado?" : "¿Marcar como pendiente?",
                icon: <AlertTriangle className="text-blue-500" />,
                content: newStatus
                    ? "El pago será marcado como pagado."
                    : "El pago será marcado como pendiente.",
                okText: newStatus
                    ? "Sí, marcar como pagado"
                    : "Sí, marcar como pendiente",
                okType: "primary",
                cancelText: "Cancelar",
                onOk() {
                    toggleMutation.mutate({ pid, isPaid: newStatus });
                },
            });
        }
    };

    return {
        togglePaymentStatus: confirmToggle,
        isLoading: toggleMutation.isPending,
    };
};
