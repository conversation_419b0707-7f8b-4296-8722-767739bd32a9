export enum EventStage {
    PLANNING = "planning",
    LAUNCHED = "launched",
    ENROLLMENT_CLOSED = "enrollment_closed",
    FINISHED = "finished",
}

export const EventStageLabels: Record<EventStage, string> = {
    [EventStage.PLANNING]: "Planificación",
    [EventStage.LAUNCHED]: "Lanzado",
    [EventStage.ENROLLMENT_CLOSED]: "Ins. cerradas",
    [EventStage.FINISHED]: "Terminado",
};

export enum EventType {
    WORKSHOP = "workshop",
    WEBINAR = "webinar",
    HANDS_OF_WORKSHOP = "hands_of_workshop",
}

export const EventTypeLabels: Record<EventType, string> = {
    [EventType.WORKSHOP]: "Workshop",
    [EventType.WEBINAR]: "Webinar",
    [EventType.HANDS_OF_WORKSHOP]: "Taller práctico",
};

export enum EventModality {
    REMOTE = "remote",
    IN_PERSON = "in_person",
}

export const EventModalityLabels: Record<EventModality, string> = {
    [EventModality.REMOTE]: "Remoto",
    [EventModality.IN_PERSON]: "Presencial",
};

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

export type EventOffering = {
    oid: string;
    codeName?: string;
    name: string;
    longName?: string;
    slug: string;
};

export type EventInstructor = {
    iid: string;
    fullName: string;
};

export type FileBase = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export type EventCoverImage = FileBase;
export type EventThumbnail = FileBase;

export type Event = {
    eid: string;
    key: string;
    offering: EventOffering;
    instructor?: EventInstructor;
    thumbnail: EventThumbnail;
    coverImage: EventCoverImage;
    name: string;
    description: string;
    modality: EventModality;
    type: EventType;
    location: string | null;
    price: string;
} & AuditBaseType;

export type CreateEventFormBody = {
    name: string;
    description?: string;
    stage: EventStage;
    modality: EventModality;
    type: EventType;
    offering: string;
    instructor: string;
    location?: string;
    price?: string;
};

export type CreateEventFormValues = CreateEventFormBody & {
    completeInfo: boolean;
};

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;
export type ListEventQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
};