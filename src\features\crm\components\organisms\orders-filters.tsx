import { <PERSON><PERSON>, <PERSON><PERSON>icker, Drawer, Radio, Select, Space, Tag } from "antd";
import {
    OrderStage,
    OrderCurrency,
    OrderStageLabels,
    OrderCurrencyLabels,
} from "@/features/crm/types/order";
import { useSearchParams } from "react-router-dom";
import { useCallback, useEffect, useState } from "react";
import { useOfferings } from "@/features/crm/hooks/use-offering";
import { Offering } from "@myTypes/offering";
import dayjs, { Dayjs } from "dayjs";

const getStageColor = (stage: OrderStage) => {
    switch (stage) {
        case OrderStage.PROSPECT:
            return "blue";
        case OrderStage.INTERESTED:
            return "green";
        case OrderStage.TO_PAY:
            return "orange";
        case OrderStage.SOLD:
            return "purple";
        case OrderStage.LOST:
            return "red";
        default:
            return "default";
    }
};

interface OrdersFiltersProps {
    isOpen: boolean;
    onClose: () => void;
}

export default function OrdersFilters({ isOpen, onClose }: OrdersFiltersProps) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Local filter states
    const [selectedStages, setSelectedStages] = useState<OrderStage[]>([]);
    const [selectedCurrency, setSelectedCurrency] = useState<string>("all");
    const [selectedOfferings, setSelectedOfferings] = useState<string[]>([]);
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs] | null>(null);

    // Load offerings
    const { offerings, isLoading: isLoadingOfferings } = useOfferings();

    // Initialize filters from URL
    useEffect(() => {
        const stages = searchParams.get("stages");
        const currency = searchParams.get("currency") || "all";
        const offeringsParam = searchParams.get("offerings");
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");

        if (stages) {
            setSelectedStages(stages.split(",") as OrderStage[]);
        } else {
            setSelectedStages(Object.values(OrderStage));
        }

        setSelectedCurrency(currency);

        if (offeringsParam) {
            setSelectedOfferings(offeringsParam.split(","));
        }

        if (startDate && endDate) {
            setDateRange([dayjs(startDate), dayjs(endDate)]);
        }
    }, [searchParams]);

    const handleStageChange = useCallback((stages: OrderStage[]) => {
        setSelectedStages(stages);
    }, []);

    const handleApplyFilters = useCallback(() => {
        setSearchParams((prev) => {
            // Clear previous filters
            prev.delete("stages");
            prev.delete("currency");
            prev.delete("offerings");
            prev.delete("startDate");
            prev.delete("endDate");

            // Apply new filters
            if (
                selectedStages.length > 0 &&
                selectedStages.length < Object.values(OrderStage).length
            ) {
                prev.set("stages", selectedStages.join(","));
            }

            if (selectedCurrency !== "all") {
                prev.set("currency", selectedCurrency);
            }

            if (selectedOfferings.length > 0) {
                prev.set("offerings", selectedOfferings.join(","));
            }

            if (dateRange && dateRange[0] && dateRange[1]) {
                prev.set("startDate", dateRange[0].format("YYYY-MM-DD"));
                prev.set("endDate", dateRange[1].format("YYYY-MM-DD"));
            }

            // Reset to page 1
            prev.set("page", "1");

            return prev;
        });
        onClose();
    }, [
        selectedStages,
        selectedCurrency,
        selectedOfferings,
        dateRange,
        setSearchParams,
        onClose,
    ]);

    const handleClearFilters = useCallback(() => {
        setSelectedStages(Object.values(OrderStage));
        setSelectedCurrency("all");
        setSelectedOfferings([]);
        setDateRange(null);

        setSearchParams((prev) => {
            prev.delete("stages");
            prev.delete("currency");
            prev.delete("offerings");
            prev.delete("startDate");
            prev.delete("endDate");
            prev.set("page", "1");
            return prev;
        });
        onClose();
    }, [setSearchParams, onClose]);

    return (
        <Drawer
            title="Aplicar filtros"
            placement="right"
            closable={true}
            onClose={onClose}
            open={isOpen}
            width={480}
        >
            <div className="space-y-4">
                <div>
                    <h4 className="font-medium mb-2">Etapas</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar etapas"
                        value={selectedStages}
                        onChange={handleStageChange}
                        allowClear
                        showSearch={false}
                    >
                        {Object.values(OrderStage).map((stage) => (
                            <Select.Option key={stage} value={stage}>
                                <div className="flex items-center gap-2">
                                    <Tag
                                        color={getStageColor(stage)}
                                        style={{ margin: 0 }}
                                    >
                                        {OrderStageLabels[stage]}
                                    </Tag>
                                </div>
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Moneda</h4>
                    <Radio.Group
                        value={selectedCurrency}
                        onChange={(e) => setSelectedCurrency(e.target.value)}
                    >
                        <Space direction="vertical">
                            <Radio value="all">Todas</Radio>
                            <Radio value={OrderCurrency.USD}>
                                {OrderCurrencyLabels[OrderCurrency.USD]} (USD)
                            </Radio>
                            <Radio value={OrderCurrency.PEN}>
                                {OrderCurrencyLabels[OrderCurrency.PEN]} (PEN)
                            </Radio>
                        </Space>
                    </Radio.Group>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Programas</h4>
                    <Select
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Seleccionar programas"
                        value={selectedOfferings}
                        onChange={setSelectedOfferings}
                        loading={isLoadingOfferings}
                        showSearch
                        filterOption={(input, option) =>
                            (option?.label as string)
                                ?.toLowerCase()
                                .includes(input.toLowerCase())
                        }
                    >
                        {offerings.map((offering: Offering) => (
                            <Select.Option key={offering.oid} value={offering.oid}>
                                {offering.name}
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div>
                    <h4 className="font-medium mb-2">Rango de fecha</h4>
                    <DatePicker.RangePicker
                        style={{ width: "100%" }}
                        placeholder={["Fecha inicio", "Fecha fin"]}
                        value={dateRange}
                        onChange={(dates) =>
                            setDateRange(dates as [Dayjs, Dayjs] | null)
                        }
                    />
                </div>

                <div className="pt-4 space-y-2">
                    <Button type="primary" block onClick={handleApplyFilters}>
                        Aplicar filtros
                    </Button>
                    <Button block onClick={handleClearFilters}>
                        Limpiar filtros
                    </Button>
                </div>
            </div>
        </Drawer>
    );
}
