import { useEffect, useRef } from "react";
import { Card, Form, DatePicker, Select, Button, Tooltip } from "antd";
import { Filter, RefreshCwIcon } from "lucide-react";
import { OrderStage, OrderStageLabels } from "@/features/crm/types/order";
import locale from "antd/es/date-picker/locale/es_ES";
import dayjs, { Dayjs } from "dayjs";
import { useMutateSearchParams } from "@hooks/use-mutate-search-params";
import { parseFilterToRecord } from "@lib/filters";
import Reload from "@assets/icons/huge/reload.svg?react";
import "dayjs/locale/es";
import { useDashboardOrdersInvalidateCache } from "../../hooks/use-dashboard-orders";
import { useNavigate } from "react-router-dom";
import { DashboardOrdersFilterOptions } from "../../types/dashboard/orders";
import { DefaultOptionType } from "antd/es/select";

dayjs.locale("es");

const { RangePicker } = DatePicker;
const { Option } = Select;

interface FilterValues {
    createdAt?: [Dayjs, Dayjs] | null;
    stages?: OrderStage[];
    products?: string[];
    salesAgent?: string;
    minAmount?: number;
    maxAmount?: number;
}

interface OrderFilterCardProps {
    filterOptions?: DashboardOrdersFilterOptions;
}

export default function OrderFilterCard({ filterOptions }: OrderFilterCardProps) {
    const [form] = Form.useForm<FilterValues>();
    const { searchParams, mutateManySearchParams } = useMutateSearchParams();
    const navigate = useNavigate();

    // Filtros de opciones
    const offerings = filterOptions?.products || [];
    const salesAgents = filterOptions?.salesAgents || [];

    // Capturar valores iniciales
    const initialValuesRef = useRef<FilterValues | null>(null);

    if (initialValuesRef.current === null) {
        const values: FilterValues = {
            ...Object.fromEntries(searchParams.entries()),
        };

        // Parse stages
        values.stages = searchParams.get("stages")?.split(",") as OrderStage[];

        // Parse products
        values.products = searchParams.get("products")?.split(",");

        // Parse amounts
        const minAmount = searchParams.get("minAmount");
        const maxAmount = searchParams.get("maxAmount");
        if (minAmount) {
            values.minAmount = parseInt(minAmount);
        }
        if (maxAmount) {
            values.maxAmount = parseInt(maxAmount);
        }

        // Date range
        const createdAtAfter = searchParams.get("createdAtAfter");
        const createdAtBefore = searchParams.get("createdAtBefore");
        if (createdAtAfter && createdAtBefore) {
            values.createdAt = [dayjs(createdAtAfter), dayjs(createdAtBefore)];
        } else {
            // Esta semana por defecto
            values.createdAt = [dayjs().startOf("week"), dayjs().endOf("week")];
        }

        initialValuesRef.current = values;
    }

    const getInitialValues = () => {
        if (!initialValuesRef.current) return {};
        return {
            ...initialValuesRef.current,
            createdAt: [
                initialValuesRef.current.createdAt?.[0] as Dayjs,
                initialValuesRef.current.createdAt?.[1] as Dayjs,
            ],
        };
    };

    // Establecer valores iniciales solo una vez
    useEffect(() => {
        if (initialValuesRef.current) {
            form.setFieldsValue(getInitialValues());
        }
    }, [form]);

    const handleApplyFilters = () => {
        const values = form.getFieldsValue();
        const newParams = parseFilterToRecord(values);
        mutateManySearchParams(newParams);
    };

    const handleClearFilters = () => {
        // Valores por defecto: rango de fechas "esta semana"
        const defaultDateRange: [Dayjs, Dayjs] = [
            dayjs().startOf("week"),
            dayjs().endOf("week"),
        ];

        const defaultValues = {
            createdAt: defaultDateRange,
            stages: undefined,
            products: undefined,
            salesAgent: undefined,
            minAmount: undefined,
            maxAmount: undefined,
        };

        // Limpiar los search params y resetear el formulario al mismo tiempo
        const emptyParams: Record<string, string | null> = {
            createdAtAfter: defaultDateRange[0].format("YYYY-MM-DD"),
            createdAtBefore: defaultDateRange[1].format("YYYY-MM-DD"),
            stages: null,
            products: null,
            salesAgent: null,
            minAmount: null,
            maxAmount: null,
        };

        form.setFieldsValue(defaultValues);
        mutateManySearchParams(emptyParams);
    };

    const { mutateAsync: invalidateCache } = useDashboardOrdersInvalidateCache();
    const handleInvalidateCache = async () => {
        await invalidateCache();
        navigate(0);
    };

    const filterOfferingOption = (
        input: string,
        option: DefaultOptionType | undefined,
    ) => {
        const searchText = input.toLowerCase();
        const offeringName =
            offerings.find((off) => off.oid === option?.value)?.name?.toLowerCase() ||
            "";
        return offeringName.includes(searchText);
    };

    return (
        <Card
            className="shadow-md mb-6"
            title={
                <div className="flex items-center">
                    <Filter className="mr-2 h-5 w-5 text-blue-500" />
                    <span>Filtrar órdenes</span>
                </div>
            }
            extra={
                <Tooltip
                    title="Fuerza la actualización de los datos desde el servidor. Úsalo solo cuando sea necesario para no sobrecargar el sistema."
                    placement="bottomRight"
                >
                    <Button onClick={handleInvalidateCache}>
                        <Reload />
                        <span className="sr-only">Forzar actualización</span>
                    </Button>
                </Tooltip>
            }
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={undefined}
                onFinish={handleApplyFilters}
            >
                <div className="flex flex-wrap gap-4">
                    <div className="flex-grow min-w-[200px]">
                        <Form.Item
                            name="createdAt"
                            label="Fecha de creación"
                            className="mb-0"
                        >
                            <RangePicker
                                className="w-full"
                                locale={locale}
                                presets={[
                                    {
                                        label: "Hoy",
                                        value: [dayjs(), dayjs()],
                                    },
                                    {
                                        label: "Esta semana",
                                        value: [
                                            dayjs().startOf("week"),
                                            dayjs().endOf("week"),
                                        ],
                                    },
                                    {
                                        label: "Este mes",
                                        value: [
                                            dayjs().startOf("month"),
                                            dayjs().endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Último mes",
                                        value: [
                                            dayjs()
                                                .subtract(1, "month")
                                                .startOf("month"),
                                            dayjs().subtract(1, "month").endOf("month"),
                                        ],
                                    },
                                    {
                                        label: "Este año",
                                        value: [
                                            dayjs().startOf("year"),
                                            dayjs().endOf("year"),
                                        ],
                                    },
                                ]}
                            />
                        </Form.Item>
                    </div>

                    <div className="min-w-[160px]">
                        <Form.Item name="stages" label="Etapas" className="mb-0">
                            <Select
                                mode="multiple"
                                placeholder="Seleccionar"
                                className="w-full max-w-[160px] overflow-hidden"
                                allowClear
                                maxTagCount={"responsive"}
                            >
                                {Object.entries(OrderStageLabels).map(
                                    ([value, label]) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ),
                                )}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="min-w-[160px]">
                        <Form.Item name="products" label="Programas" className="mb-0">
                            <Select
                                mode="multiple"
                                placeholder="Seleccionar"
                                className="w-full max-w-[160px] overflow-hidden"
                                allowClear
                                maxTagCount={"responsive"}
                                filterOption={filterOfferingOption}
                                popupClassName="min-w-[500px]"
                            >
                                {offerings.map((offering) => (
                                    <Option key={offering.oid} value={offering.oid}>
                                        <span className="whitespace-normal break-words leading-tight">
                                            {offering.name}
                                        </span>
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="min-w-[150px]">
                        <Form.Item name="salesAgent" label="Vendedor" className="mb-0">
                            <Select
                                placeholder="Seleccionar"
                                className="w-full"
                                allowClear
                            >
                                {salesAgents.map((agent) => (
                                    <Option key={agent.uid} value={agent.uid}>
                                        {agent.fullName}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="flex justify-end items-end gap-2 mt-4">
                        <Button
                            type="default"
                            icon={<RefreshCwIcon className="h-4 w-4" />}
                            onClick={handleClearFilters}
                        >
                            Limpiar filtros
                        </Button>
                        <Button type="primary" htmlType="submit">
                            Aplicar filtros
                        </Button>
                    </div>
                </div>
            </Form>
        </Card>
    );
}
