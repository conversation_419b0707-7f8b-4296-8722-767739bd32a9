import { getMajors, createMajor } from "@/features/crm/services/portals/major";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { Button, Divider, Form, Input, Modal, Select, SelectProps, App } from "antd";
import { Plus } from "lucide-react";
import { useState } from "react";

interface SelectMajorProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}

export default function SelectMajor({
    value,
    onChange,
    ...restProps
}: SelectMajorProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    // Query to fetch educational institutions
    const { data, isLoading } = useQuery({
        queryKey: ["majors"],
        queryFn: async () => getMajors(),
    });

    // Mutation for creating a new major
    const { mutate: createMajorMutation, isPending: isCreating } = useMutation({
        mutationFn: createMajor,
        onSuccess: (newMajor) => {
            notification.success({
                message: "Carrera profesional creada",
                description: "La carrera profesional ha sido creada exitosamente",
            });
            setIsModalOpen(false);
            form.resetFields();
            // Invalidate and refetch majors
            queryClient.invalidateQueries({ queryKey: ["majors"] });
            // Select the newly created major
            if (onChange) {
                onChange(newMajor.mid);
            }
        },
        onError: () => {
            notification.error({
                message: "Error al crear la carrera profesional",
                description:
                    "Ha ocurrido un error al intentar crear la carrera profesional",
            });
        },
    });

    const { results: majors } = data || {
        results: [],
    };

    const majorsOptions: SelectProps["options"] =
        majors?.map((major) => ({
            value: major.mid,
            label: major.name,
        })) || [];

    // Handle change in a way that works with both Form.Item and direct usage
    const handleChange = (selectedValue: string) => {
        if (onChange) {
            onChange(selectedValue);
        }
    };

    const handleCreateMajor = async (values: { name: string }) => {
        createMajorMutation(values.name);
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={handleChange}
                options={majorsOptions}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras la Carrera profesional?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setIsModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />

            <Modal
                title="Agregar Carrera Profesional"
                open={isModalOpen}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                footer={null}
                confirmLoading={isCreating}
            >
                <Form form={form} onFinish={handleCreateMajor} layout="vertical">
                    <Form.Item
                        name="name"
                        label="Nombre de la Carrera"
                        rules={[
                            {
                                required: true,
                                message: "Por favor ingrese el nombre de la carrera",
                            },
                        ]}
                    >
                        <Input
                            placeholder="Ingrese el nombre de la carrera"
                            disabled={isCreating}
                        />
                    </Form.Item>
                    <Form.Item className="mb-0 text-right">
                        <Button
                            type="default"
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                            className="mr-2"
                            disabled={isCreating}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isCreating}>
                            Guardar
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
}
