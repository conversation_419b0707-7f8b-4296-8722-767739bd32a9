import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Tabs, TabsProps } from "antd";
import ContactsDashboardTab from "./components/organisms/contacts-tab";
import SalesDashboardTab from "./components/organisms/sales-tab";
import PaymentsDashboardTab from "./components/organisms/payments-tab";
import { useSearchParams } from "react-router-dom";

export default function CrmDashboardPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const tabKey = searchParams.get("tab") || "contacts";

    const tabItems: TabsProps["items"] = [
        {
            key: "contacts",
            label: "Contactos",
            children: <ContactsDashboardTab />,
        },
        {
            key: "orders",
            label: "Órdenes",
            children: <SalesDashboardTab />,
        },
        {
            key: "payments",
            label: "Pagos",
            children: <PaymentsDashboardTab />,
        },
        {
            key: "events",
            label: "Eventos",
            children: <div>Eventos</div>,
        },
    ];

    const handleSetCurrentTab = (key: string) => {
        setSearchParams({ tab: key });
    };

    return (
        <CrmLayout>
            <div className="max-w-screen-2xl w-full">
                <WelcomeBar helperText="Bienvenido a reportería de CRM" />
                <Tabs
                    items={tabItems}
                    defaultActiveKey={tabKey}
                    onChange={(key) => {
                        handleSetCurrentTab(key);
                    }}
                />
            </div>
        </CrmLayout>
    );
}
