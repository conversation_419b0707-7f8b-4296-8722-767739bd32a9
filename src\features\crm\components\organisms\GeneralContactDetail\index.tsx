import {
    ContactUpdateForm,
    ContactOcupation,
    ContactOcupationLabel,
    ContactRetrieve,
    ContactUpdateRequest,
} from "@/features/crm/types/contact";
import { App, Button, Form, Input, Select, Tooltip } from "antd";
import PhoneInput from "antd-phone-input";
import { Mail, RefreshCcwDot, Save, Trash } from "lucide-react";
import type { SelectProps } from "antd";
import { useMemo } from "react";
import { formatDateTime } from "@lib/helpers";
import { useMutation } from "@tanstack/react-query";
import {
    deleteContact,
    googleContactSync,
    updateContact,
} from "@/features/crm/services/portals/contact";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectEducationalInstitution from "@/features/crm/components/molecules/select-educational-institution";
import SelectMajor from "@/features/crm/components/molecules/select-major";
import queryClient from "@lib/queryClient";
import { parsePhoneNumber } from "react-phone-hooks";
import { phoneNumberToString } from "@lib/phone-number";
import UploadContactProfilePhoto from "../../molecules/upload-contact-profile-photo";
import { useNavigate } from "react-router-dom";
import SelectTerm from "../../molecules/select-term";

type GeneralContactDetailProps = {
    contact: ContactRetrieve;
};

export default function GeneralContactDetail({ contact }: GeneralContactDetailProps) {
    const [form] = Form.useForm<ContactUpdateForm>();
    const { notification, message } = App.useApp();
    const navigate = useNavigate();

    const ocupationSelectOptions: SelectProps["options"] = Object.values(
        ContactOcupation,
    ).map((value) => ({
        value,
        label: ContactOcupationLabel[value],
    }));

    const selectedOcupation = Form.useWatch("ocupation", form);
    const isEmployee = useMemo(
        () =>
            selectedOcupation === ContactOcupation.EMPLOYEE ||
            selectedOcupation === ContactOcupation.INDEPENDENT,
        [selectedOcupation],
    );

    const { mutate: onGoogleContactSync, isPending: isGoogleContactSyncLoading } =
        useMutation({
            mutationFn: (cid: string) => {
                return googleContactSync(cid);
            },
            onSuccess: (data) => {
                contact.googleContactId = data.googleContactId;
                contact.lastGoogleSync = data.lastGoogleSync;
            },
        });
    const handleGoogleSync: React.MouseEventHandler<HTMLElement> = () => {
        onGoogleContactSync(contact.uid);
    };

    const { mutate: updateContactMutate, isPending: isContactUpdateLoading } =
        useMutation({
            mutationFn: (partialContact: ContactUpdateRequest) =>
                updateContact(contact.uid, partialContact),

            onSuccess: (data) => {
                notification.success({
                    message: "Contacto actualizado",
                    description: `El contacto ${data.firstName} ${data.lastName} ha sido actualizado.`,
                    duration: 2,
                });
                queryClient.invalidateQueries({
                    queryKey: ["contacts", contact.uid],
                });
            },
        });

    const handleFormFinish = (values: ContactUpdateForm) => {
        try {
            const phoneNumberStr = phoneNumberToString(values.phoneNumber);
            updateContactMutate({
                ...values,
                phoneNumber: phoneNumberStr,
            });
        } catch (error) {
            message.error("Error al actualizar el contacto");
        }
    };

    const { mutate: deleteContactMutate, isPending: isDeletePending } = useMutation({
        mutationFn: (cid: string) => deleteContact(cid),
        onSuccess: () => {
            notification.success({
                message: "Contacto eliminado",
                description: `El contacto ${contact.firstName} ${contact.lastName} ha sido eliminado.`,
                duration: 2,
            });
            navigate("/crm/contacts");
            queryClient.invalidateQueries({
                queryKey: ["contacts", contact.uid],
            });
        },
        onError: () => {
            notification.error({
                message: "Error al eliminar el contacto",
                description: `El contacto ${contact.firstName} ${contact.lastName} no pudo ser eliminado.`,
                duration: 2,
            });
        },
    });
    const handleDeleteContact = () => {
        deleteContactMutate(contact.uid);
    };
    return (
        <Form
            name="general-contact-form"
            layout="vertical"
            form={form}
            initialValues={{
                firstName: contact.firstName,
                lastName: contact.lastName,
                email: contact.email,
                phoneNumber: parsePhoneNumber(contact.phoneNumber),
                educationalInstitution: contact.educationalInstitution?.eiid,
                ocupation: contact.ocupation,
                major: contact.major?.mid,
                term: contact.term?.tid,
                company: contact.company,
                role: contact.role,
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                        <Form.Item<ContactUpdateForm>
                            name="firstName"
                            label={<FormLabel>Nombres</FormLabel>}
                        >
                            <Input placeholder="Ej. Gerardo" />
                        </Form.Item>
                        <Form.Item<ContactUpdateForm>
                            name="lastName"
                            label={<FormLabel>Apellidos</FormLabel>}
                        >
                            <Input placeholder="Ej. Inti Lobato" />
                        </Form.Item>

                        <Form.Item<ContactUpdateForm>
                            name="phoneNumber"
                            label={<FormLabel>Teléfono</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, ingrese su número de teléfono.",
                                },
                            ]}
                        >
                            <PhoneInput enableSearch={true} country="pe" />
                        </Form.Item>
                        <Form.Item<ContactUpdateForm>
                            name="email"
                            label={<FormLabel>Correo electrónico</FormLabel>}
                        >
                            <Input
                                prefix={<Mail size={14} className="text-gray-400" />}
                                placeholder="Ej. <EMAIL>"
                                type="email"
                            />
                        </Form.Item>
                    </div>

                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN PROFESIONAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                        <Form.Item<ContactUpdateForm>
                            name="ocupation"
                            label={<FormLabel>Ocupación</FormLabel>}
                        >
                            <Select options={ocupationSelectOptions} />
                        </Form.Item>
                        <Form.Item<ContactUpdateForm>
                            name="educationalInstitution"
                            label={<FormLabel>Institución Educativa</FormLabel>}
                        >
                            <SelectEducationalInstitution />
                        </Form.Item>
                        <Form.Item<ContactUpdateForm>
                            name="major"
                            label={<FormLabel>Carrera profesional</FormLabel>}
                        >
                            <SelectMajor />
                        </Form.Item>
                        <Form.Item<ContactUpdateForm>
                            name="term"
                            label={<FormLabel>Ciclo académico</FormLabel>}
                        >
                            <SelectTerm />
                        </Form.Item>
                        {isEmployee && (
                            <>
                                <Form.Item<ContactUpdateForm>
                                    name="company"
                                    label={<FormLabel>Empresa</FormLabel>}
                                >
                                    <Input placeholder="Ej. Grupo Ceup" />
                                </Form.Item>
                                <Form.Item<ContactUpdateForm>
                                    name="role"
                                    label={<FormLabel>Puesto</FormLabel>}
                                >
                                    <Input placeholder="Ej. Gerente de ventas" />
                                </Form.Item>
                            </>
                        )}
                    </div>
                </div>
                <div className="col-span-2 space-y-2">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                block
                                disabled={isDeletePending}
                                onClick={() => handleDeleteContact()}
                            >
                                Eliminar
                            </Button>
                            <Button
                                block
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isContactUpdateLoading}
                                onClick={() => form.submit()}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <UploadContactProfilePhoto
                            cid={contact.uid}
                            initialProfilePhoto={contact.profilePhoto}
                        />
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <div className="flex justify-between items-center mb-4">
                            <p className="text-gray-400 font-semibold text-sm">
                                CONTACTO DE GOOGLE
                            </p>
                            <Tooltip title="Sincronizar con Google">
                                <Button
                                    loading={isGoogleContactSyncLoading}
                                    icon={<RefreshCcwDot className="text-blue-500" />}
                                    type="text"
                                    shape="circle"
                                    onClick={handleGoogleSync}
                                />
                            </Tooltip>
                        </div>

                        <div className="space-y-3">
                            <div className="border-l-4 border-blue-500 pl-3">
                                <FormLabel className="mb-1">
                                    ID de Google Contactos
                                </FormLabel>
                                <p className="text-sm font-medium truncate">
                                    {contact.googleContactId || "-"}
                                </p>
                            </div>

                            <div className="border-l-4 border-green-500 pl-3">
                                <p className="font-semibold text-sm text-gray-500 mb-1">
                                    Última sincronización
                                </p>
                                <p className="text-sm font-medium">
                                    {formatDateTime(contact.lastGoogleSync) || "-"}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
}
