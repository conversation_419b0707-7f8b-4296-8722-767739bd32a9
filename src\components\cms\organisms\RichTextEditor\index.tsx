import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import YooptaEditor, { createYooptaEditor, YooptaContentValue } from "@yoopta/editor";
import { Bold, Italic, CodeMark, Underline, Strike, Highlight } from "@yoopta/marks";
import Paragraph from "@yoopta/paragraph";
import Blockquote from "@yoopta/blockquote";
import { HeadingOne, HeadingTwo, HeadingThree } from "@yoopta/headings";
import { NumberedList, BulletedList, TodoList } from "@yoopta/lists";
import Link from "@yoopta/link";
import Image, { ImageElement } from "@yoopta/image";
import Divider from "@yoopta/divider";
import LinkTool, { DefaultLinkToolRender } from "@yoopta/link-tool";
import ActionMenu, { DefaultActionMenuRender } from "@yoopta/action-menu-list";
import Toolbar, { DefaultToolbarRender } from "@yoopta/toolbar";
import styles from "./RichTextEditor.module.css";
import { useDebounce } from "@hooks/use-debounce";
import { useMutation } from "@tanstack/react-query";
import { UpdateBlogPostBody } from "@myTypes/blog";
import { updateBlogPost } from "@services/portals/cms/blogs/post";
import { objectHash } from "@lib/hash";
import { SavePostStatusEnum, useSavePostStore } from "@store/savePostStore";
import { deleteFile, retrieveFile, uploadFile } from "@services/portals/shared/file";
import { extractFileIdFromUrl } from "@lib/helpers";

// Configuración estática de plugins y herramientas
const Headings = [HeadingOne, HeadingTwo, HeadingThree];
const Lists = [NumberedList, BulletedList, TodoList];
const PLUGINS = [
    Link,
    Paragraph,
    Blockquote,
    Image.extend({
        options: {
            async onUpload(file) {
                const { url } = await uploadFile(file, {
                    description: file.name,
                    isUsed: true,
                });
                return {
                    src: url,
                    alt: file.name,
                };
            },
        },
        events: {
            async onDestroy(editor, blockId) {
                const block = editor.getBlock({
                    id: blockId,
                });

                const imgBlock = block?.value[0] as ImageElement;
                const imageSrc = imgBlock.props?.src;
                if (imgBlock && imageSrc) {
                    // extract fileId from imageSrc,
                    // ej: http://.../public/5f97ef54-f014-4274-ab9c-4d7a9d82c939/PPCEU.webp -> 5f97ef54-f014-4274-ab9c-4d7a9d82c939
                    const fileId = extractFileIdFromUrl(imageSrc);
                    if (!fileId) return;

                    const { fid } = await retrieveFile(fileId);
                    if (fid) {
                        await deleteFile(fid); // delete from db and storage
                    }
                }
            },
        },
    }),
    Divider,
]
    .concat(Headings)
    .concat(Lists);

const MARKS = [Bold, Italic, CodeMark, Underline, Strike, Highlight];

const TOOLS = {
    Toolbar: {
        tool: Toolbar,
        render: DefaultToolbarRender,
    },
    ActionMenu: {
        tool: ActionMenu,
        render: DefaultActionMenuRender,
    },
    LinkTool: {
        tool: LinkTool,
        render: DefaultLinkToolRender,
    },
};

interface RichtextEditorProps {
    blogId: string;
    initialContent?: YooptaContentValue;
    onChange?: (value: YooptaContentValue) => void;
    placeholder?: string;
    readOnly?: boolean;
}

export default function RichtextEditor({
    blogId,
    initialContent = {},
    placeholder = "Escribe aquí...",
    readOnly = false,
}: RichtextEditorProps) {
    const [value, setValue] = useState<YooptaContentValue>(
        initialContent || DEFAULT_EMPTY_CONTENT,
    );
    const [editorId, setEditorId] = useState<string | null>(blogId);

    const editor = useMemo(() => createYooptaEditor(), []);
    const selectionRef = useRef(null);
    const debouncedContent = useDebounce(value, 2000);
    const { save, saveStatus } = useSavePostStore();

    const contentHashRef = useRef<string>(
        objectHash(initialContent as Record<string, unknown>),
    );
    const userChangedContentRef = useRef<boolean>(false);

    const { mutateAsync: updateBlog } = useMutation({
        mutationFn: (data: UpdateBlogPostBody) => updateBlogPost(blogId, data),
    });

    // Update the editor values
    useEffect(() => {
        if(blogId){
            setValue(initialContent);
            setEditorId(blogId);
        }
    }, [initialContent, editor, blogId]);

    const saveChanges = useCallback(async () => {
        if (saveStatus === SavePostStatusEnum.SAVING) return;
        try {
            await save(() =>
                updateBlog({
                    content: debouncedContent,
                }),
            );
            // Update the reference hash after saving
            contentHashRef.current = objectHash(
                debouncedContent as Record<string, unknown>,
            );
            userChangedContentRef.current = false;
        } catch (error) {
            console.error("Error al validar formulario:", error);
        }
    }, [debouncedContent, updateBlog, save, saveStatus]);

    // Check for content changes and save if needed
    useEffect(() => {
        const currentHash = objectHash(debouncedContent as Record<string, unknown>);
        if (currentHash !== contentHashRef.current && userChangedContentRef.current) {
            saveChanges();
        }
    }, [debouncedContent, saveChanges]);

    function handleChange(newValue: YooptaContentValue) {
        setValue(newValue);
        userChangedContentRef.current = true;
    }

    return (
        <div ref={selectionRef}>
            <YooptaEditor
                key={editorId}
                editor={editor}
                plugins={PLUGINS}
                marks={MARKS}
                tools={TOOLS}
                value={value}
                onChange={handleChange}
                placeholder={placeholder}
                selectionBoxRoot={selectionRef}
                readOnly={readOnly}
                className={`${styles.editor} min-w-full`}
                autoFocus
            />
        </div>
    );
}

const DEFAULT_EMPTY_CONTENT: YooptaContentValue = {
    "e8d9d853-d3b9-4a54-a1d1-298a559f203a": {
        id: "e8d9d853-d3b9-4a54-a1d1-298a559f203a",
        meta: {
            align: "left",
            depth: 0,
            order: 0,
        },
        type: "Paragraph",
        value: [
            {
                id: "dfe9490b-6899-4fb6-a804-4290c2d015f4",
                type: "paragraph",
                children: [
                    {
                        text: "",
                    },
                ],
                props: {
                    nodeType: "block",
                },
            },
        ],
    },
};
