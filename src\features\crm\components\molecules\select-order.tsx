import { Select, SelectProps } from "antd";
import { useOrders } from "../../hooks/use-order";
import { Link } from "react-router-dom";
import { ExternalLink } from "lucide-react";

interface SelectOrderProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
}
export default function SelectOrder({
    value,
    onChange,
    ...restProps
}: SelectOrderProps) {
    const { orders } = useOrders();
    const orderOptions: SelectProps["options"] = orders?.map((order) => ({
        value: order.oid,
        label: (
            <div className="flex justify-between items-center">
                <span>{order.owner.fullName}</span>
                <span className="text-xs text-gray-600">#{order.oid?.slice(-6)}</span>
                <Link to={`/crm/orders/${order.oid}`} title="View Contact">
                    <ExternalLink size={14} />
                </Link>
            </div>
        ),
        data: {
            ...order,
        },
    }));
    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                options={orderOptions}
            />
        </>
    );
}
