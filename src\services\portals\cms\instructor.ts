import { filtersToUrlParams } from "@lib/helpers";
import { PaginatedResponse, PaginationAndFilterParams } from "@myTypes/base";
import {
    CreateInstructor,
    UpdateInstructor,
    RetrieveInstructor,
    Instructor,
} from "@myTypes/instructor";
import { instructorQueryFilterSchema } from "@pages/cms/InstructorListPage/filters";
import { portalsApi } from "@services/portals";

const DEFAULT_PAGE = 1;
const DEFAULT_PAGE_SIZE = 10;

export const getInstructors = async ({
    page = DEFAULT_PAGE,
    pageSize = DEFAULT_PAGE_SIZE,
    filters = {},
}: PaginationAndFilterParams): Promise<PaginatedResponse<Instructor>> => {
    const parsedFilters = instructorQueryFilterSchema.safeParse(filters);
    const filterParams = filtersToUrlParams({
        ...parsedFilters.data,
    });

    const response = await portalsApi.get(`cms/instructors`, {
        params: {
            page,
            pageSize,
            ...filterParams,
        },
    });
    return response.data;
};

export const retrieveInstructor = async (
    iid: string | undefined,
): Promise<RetrieveInstructor> => {
    if (!iid) {
        throw new Error("No instructor ID provided");
    }

    const response = await portalsApi.get(`cms/instructors/${iid}`);
    return response.data;
};

export const createInstructor = async (data: CreateInstructor) => {
    const formData = new FormData();
    const { profilePhotoFile, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    formData.append("profilePhotoFile", profilePhotoFile[0] as unknown as Blob);
    const response = await portalsApi.post("cms/instructors", formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const updateInstructor = async (id: string, data: UpdateInstructor) => {
    const formData = new FormData();
    const { profilePhotoFile, ...payload } = data;

    Object.entries(payload).forEach(([key, value]) => {
        if (value) {
            formData.append(key, value as string);
        }
    });

    if (profilePhotoFile) {
        formData.append("profilePhotoFile", profilePhotoFile[0] as unknown as Blob);
    }
    const response = await portalsApi.patch(`cms/instructors/${id}`, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
    return response.data;
};

export const deleteInstructor = async (id: string) => {
    const response = await portalsApi.delete(`cms/instructor/${id}`);
    return response.data;
};

export const bulkDeleteInstructors = async (ids: number[]) => {
    const response = await portalsApi.delete("cms/instructor/bulk_delete", {
        data: {
            ids,
        },
    });
    return response.data;
};
