import { useMutation } from "@tanstack/react-query";
import { type AxiosError } from "axios";
import { CreateOrderItem, OrderItem } from "../types/order";
import queryClient from "@lib/queryClient";
import {
    createOrderItem,
    deleteOrderItem,
    partialUpdateOrderItem,
    sendClassroomInvitation,
} from "../services/portals/order-item";

type UseCreateOrderItemProps = {
    onCreateOrderSuccess?: (item: OrderItem) => void;
    onCreateOrderError?: (error: AxiosError) => void;
};

export const useCreateOrderItem = ({
    onCreateOrderSuccess,
    onCreateOrderError,
}: UseCreateOrderItemProps = {}) => {
    return useMutation<OrderItem, AxiosError, CreateOrderItem>({
        mutationFn: (orderItem) => createOrderItem(orderItem),

        onSuccess: (item, variables) => {
            onCreateOrderSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.order] });
        },
        onError: (error) => {
            onCreateOrderError?.(error);
        },
    });
};

type UseUpdateOrdenItemProps = {
    onUpdateOrderItemSuccess?: (item: Partial<OrderItem>) => void;
    onUpdateOrderItemError?: () => void;
};

export const useUpdateOrderItem = ({
    onUpdateOrderItemSuccess,
    onUpdateOrderItemError,
}: UseUpdateOrdenItemProps = {}) => {
    return useMutation<
        Partial<OrderItem>,
        AxiosError,
        { oid: string; orderItem: Partial<OrderItem> }
    >({
        mutationFn: ({ orderItem }) => partialUpdateOrderItem(orderItem),

        onSuccess: (item, variables) => {
            onUpdateOrderItemSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: () => {
            onUpdateOrderItemError?.();
        },
    });
};

type UseDeleteOrderItemProps = {
    onDeleteOrderItemSuccess?: (item: OrderItem) => void;
    onDeleteOrderItemError?: () => void;
};

export const useDeleteOrderItem = ({
    onDeleteOrderItemSuccess,
    onDeleteOrderItemError,
}: UseDeleteOrderItemProps = {}) => {
    return useMutation<OrderItem, AxiosError, { oid: string; orderItem: OrderItem }>({
        mutationFn: async ({ orderItem }) => {
            await deleteOrderItem(orderItem);
            return orderItem;
        },

        onSuccess: (item, variables) => {
            onDeleteOrderItemSuccess?.(item);
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: () => {
            onDeleteOrderItemError?.();
        },
    });
};

type UseSendClassroomInvitationProps = {
    onSendInvitationSuccess?: () => void;
    onSendInvitationError?: (error: AxiosError) => void;
};

export const useSendClassroomInvitation = ({
    onSendInvitationSuccess,
    onSendInvitationError,
}: UseSendClassroomInvitationProps = {}) => {
    return useMutation<void, AxiosError, { oid: string; orderItemId: string }>({
        mutationFn: async ({ orderItemId }) => {
            await sendClassroomInvitation(orderItemId);
        },

        onSuccess: (_, variables) => {
            onSendInvitationSuccess?.();
            queryClient.invalidateQueries({ queryKey: ["order", variables.oid] });
        },
        onError: (error) => {
            onSendInvitationError?.(error);
        },
    });
};
