import {
    App,
    Button,
    Form,
    message,
    Modal,
    Radio,
    Steps,
    StepsProps,
    Tooltip,
} from "antd";
import SelectContact from "@/features/crm/components/molecules/select-contact";
import {
    OrderPartialUpdate,
    OrderStage,
    OrderStageLabels,
    PartialUpdateOrderValues,
    RetrieveOrder,
} from "@/features/crm/types/order";
import { formatDateTime } from "@lib/helpers";
import OrderItemsTable from "./order-items-table";
import SelectStaffUser from "@/features/crm/components/molecules/select-staff-user";
import { Link2, PlusCircle, Save, Trash } from "lucide-react";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectOrderBenefits from "../molecules/select-order-benefits";
import SelectOrderLeadSources from "../molecules/select-order-lead-sources";
import EditStageDatePopover from "../molecules/edit-stage-date";
import EditOrderStage from "../molecules/edit-order-stage";
import { useMemo, useRef, useState } from "react";
import AddOrderItemForm, { AddOrderItemFormRef } from "./add-order-item-form";
import { Link } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { partialUpdateOrder } from "../../services/portals/order";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import Reload from "@assets/icons/huge/reload.svg?react";
import { useSendClassroomInvitations } from "../../hooks/use-order";
import { openErrorNotification } from "@lib/notification";
import type { AxiosError } from "axios";

type EditOrderFormProps = {
    order: RetrieveOrder;
};

export default function EditOrderForm({ order }: EditOrderFormProps) {
    const [form] = Form.useForm<PartialUpdateOrderValues>();
    const [api, contextHolder] = message.useMessage();
    const queryClient = useQueryClient();
    const { notification } = App.useApp();

    const regularStepItems: StepsProps["items"] = [
        OrderStage.PROSPECT,
        OrderStage.INTERESTED,
        OrderStage.TO_PAY,
        OrderStage.SOLD,
    ].map((stageType) => {
        const stageInfo = order?.stagesDates.find((s) => s.stage === stageType);

        return {
            title: OrderStageLabels[stageType],
            status: stageType === order.stage ? "finish" : "wait",
            description: (
                <div className="flex flex-col gap-1">
                    {stageInfo && (
                        <span className="text-xs text-gray-500">
                            {formatDateTime(stageInfo.date)}
                        </span>
                    )}
                    <div className="flex items-center justify-between">
                        {stageType === order.stage ? (
                            <span className="flex items-center gap-1">
                                <span className="text-blue-full font-medium text-xs">
                                    Actual
                                </span>
                                <EditStageDatePopover stage={stageType} order={order} />
                            </span>
                        ) : (
                            <span className="flex items-center gap-1">
                                <EditOrderStage
                                    stage={stageType}
                                    currentStage={order.stage}
                                    order={order}
                                />
                                <EditStageDatePopover stage={stageType} order={order} />
                            </span>
                        )}
                    </div>
                </div>
            ),
        };
    });
    const lostStageInfo = order?.stagesDates.find(
        (s) => s.stage === OrderStage.LOST && s.date,
    );
    const lostStagItems: StepsProps["items"] = useMemo(
        () =>
            lostStageInfo
                ? [
                      {
                          title: OrderStageLabels[OrderStage.LOST],
                          status: order.stage === OrderStage.LOST ? "error" : "wait",
                          description: (
                              <div className="flex flex-col gap-1">
                                  {lostStageInfo && (
                                      <span className="text-xs text-gray-500">
                                          {formatDateTime(lostStageInfo.date)}
                                      </span>
                                  )}
                                  <div className="flex items-center justify-between">
                                      {order.stage === OrderStage.LOST ? (
                                          <span className="flex items-center gap-1">
                                              <span className="text-red-500 font-medium text-xs">
                                                  Actual
                                              </span>
                                              <EditStageDatePopover
                                                  stage={OrderStage.LOST}
                                                  order={order}
                                              />
                                          </span>
                                      ) : (
                                          <EditOrderStage
                                              stage={OrderStage.LOST}
                                              currentStage={order.stage}
                                              order={order}
                                          />
                                      )}
                                  </div>
                              </div>
                          ),
                      },
                  ]
                : [],
        [lostStageInfo, order],
    );

    const stepItems: StepsProps["items"] = useMemo(
        () =>
            lostStagItems ? [...regularStepItems, ...lostStagItems] : regularStepItems,
        [regularStepItems, lostStagItems],
    );

    const [modalAddOrderItemOpen, setModalAddOrderItemOpen] = useState(false);
    const addOrderItemFormRef = useRef<AddOrderItemFormRef>(null);

    const handleModalOk = () => {
        addOrderItemFormRef.current?.submit();
    };

    const handleAddOrderItem: React.MouseEventHandler<SVGSVGElement> = () => {
        setModalAddOrderItemOpen(true);
    };

    const { mutate: updateOrderMutate, isPending } = useMutation({
        mutationKey: ["update-order", order.oid],
        mutationFn: (payload: OrderPartialUpdate) =>
            partialUpdateOrder(order.oid, payload),
        onSuccess: () => {
            onSuccessMessage("La orden se actualizó correctamente", api);
            queryClient.invalidateQueries({ queryKey: ["order", order.oid] });
        },
        onError: () => {
            onErrorMessage("Ha ocurrido un error al actualizar la orden", api);
        },
    });

    const handleFormFinish = (values: PartialUpdateOrderValues) => {
        updateOrderMutate(values);
    };

    const handleOrderItemSuccess = () => {
        setModalAddOrderItemOpen(false);
    };

    const { mutate: syncClassroomInvitations } = useSendClassroomInvitations({
        onSendInvitationsSuccess() {
            notification.success({
                message: "Invitaciones reenviadas correctamente",
            });
        },
        onSendInvitationsError(error: AxiosError) {
            const errorMessage = error.response?.data as {
                success: boolean;
                message: string;
            };

            openErrorNotification(
                "Error al reenviar la invitación",
                errorMessage.message || "Error al procesar las invitaciones",
                notification,
            );
        },
    });

    const handleSyncOrderItems = () => {
        // Sends google Classroom invitation to all order items if the invitation status is error
        syncClassroomInvitations({
            oid: order.oid,
        });
    };

    return (
        <>
            {contextHolder}
            <Form
                name="edit-order-form"
                layout="vertical"
                form={form}
                initialValues={{
                    owner: order?.owner.uid,
                    salesAgent: order?.salesAgent?.uid,
                    benefits: order?.benefits.map((benefit) => benefit?.bid),
                    leadSources: order?.leadSources.map(
                        (leadSource) => leadSource?.lsid,
                    ),
                    agreedTotal: order?.agreedTotal,
                    isInternational: order?.isInternational,
                    hasFullScholarship: order?.hasFullScholarship,
                }}
                onFinish={handleFormFinish}
            >
                <div className="space-y-6">
                    {/* Primera fila: Información General + (Acciones + Información Adicional) */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Información General */}
                        <div className="lg:col-span-2">
                            <div className="bg-white-full rounded-lg shadow-sm p-4">
                                <p className="text-gray-400 font-semibold text-sm">
                                    INFORMACIÓN GENERAL
                                </p>
                                <Steps items={stepItems} className="my-6" />
                                <div className="flex items-center gap-2">
                                    <Form.Item<PartialUpdateOrderValues>
                                        name="owner"
                                        label={<FormLabel>Contacto</FormLabel>}
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    "Por favor, seleccione algún contacto.",
                                            },
                                        ]}
                                        className="w-full"
                                    >
                                        <SelectContact defaultContact={order.owner} />
                                    </Form.Item>
                                    <Tooltip title="Abrir contacto">
                                        <Link
                                            to={`/crm/contacts/${order.owner.uid}`}
                                            type="link"
                                            className="text-blue-full text-xs font-semibold mt-2"
                                        >
                                            <Link2 size={14} /> Abrir
                                        </Link>
                                    </Tooltip>
                                </div>

                                <div className="flex gap-4">
                                    <Form.Item<PartialUpdateOrderValues>
                                        name="benefits"
                                        label={<FormLabel>Beneficios</FormLabel>}
                                        className="flex-1"
                                    >
                                        <SelectOrderBenefits />
                                    </Form.Item>

                                    <Form.Item<PartialUpdateOrderValues>
                                        name="isInternational"
                                        label={
                                            <FormLabel>¿Es internacional?</FormLabel>
                                        }
                                        className="flex-1"
                                    >
                                        <Radio.Group
                                            block
                                            options={[
                                                { label: "Sí", value: true },
                                                { label: "No", value: false },
                                            ]}
                                            optionType="button"
                                            buttonStyle="solid"
                                        />
                                    </Form.Item>

                                    <Form.Item<PartialUpdateOrderValues>
                                        name="hasFullScholarship"
                                        label={
                                            <FormLabel>¿Tiene beca completa?</FormLabel>
                                        }
                                        className="flex-1"
                                    >
                                        <Radio.Group
                                            block
                                            options={[
                                                { label: "Sí", value: true },
                                                { label: "No", value: false },
                                            ]}
                                            optionType="button"
                                            buttonStyle="solid"
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>

                        {/* Acciones + Información Adicional */}
                        <div className="lg:col-span-1 space-y-6">
                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                <p className="text-gray-400 font-semibold text-sm">
                                    ACCIONES
                                </p>
                                <div className="flex flex-col gap-3">
                                    <div className="flex gap-3 justify-end">
                                        <Button
                                            type="primary"
                                            size="large"
                                            style={{ fontSize: 16 }}
                                            icon={<Trash />}
                                            danger
                                            disabled={isPending}
                                        >
                                            Eliminar
                                        </Button>
                                        <Button
                                            type="primary"
                                            size="large"
                                            style={{ fontSize: 16 }}
                                            icon={<Save />}
                                            htmlType="submit"
                                            loading={isPending}
                                        >
                                            Guardar
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white-full p-5 rounded-lg shadow-sm">
                                <p className="text-gray-400 font-semibold text-sm">
                                    INFORMACIÓN ADICIONAL
                                </p>

                                <Form.Item<PartialUpdateOrderValues>
                                    name="salesAgent"
                                    label={<FormLabel>Agente de ventas</FormLabel>}
                                >
                                    <SelectStaffUser />
                                </Form.Item>

                                <Form.Item<PartialUpdateOrderValues>
                                    name="leadSources"
                                    label={<FormLabel>Fuentes del lead</FormLabel>}
                                >
                                    <SelectOrderLeadSources />
                                </Form.Item>
                            </div>
                        </div>
                    </div>

                    {/* Segunda fila: Productos (ancho completo) */}
                    <div className="bg-white-full rounded-lg shadow-sm p-4">
                        <div className="flex justify-between">
                            <p className="text-gray-400 font-semibold text-sm">
                                PRODUCTOS
                            </p>
                            <div className="flex items-center gap-2">
                                <PlusCircle
                                    className="text-blue-full hover:cursor-pointer hover:text-blue-700"
                                    onClick={handleAddOrderItem}
                                />
                                {order.stage === OrderStage.SOLD && (
                                    <Tooltip title="Sincronizar invitaciones a Google Classroom">
                                        <Reload
                                            className="text-blue-full hover:cursor-pointer hover:text-blue-700"
                                            onClick={handleSyncOrderItems}
                                        />
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                        <OrderItemsTable order={order} />
                    </div>
                </div>
            </Form>
            <Modal
                title="Agregar producto"
                open={modalAddOrderItemOpen}
                onOk={handleModalOk}
                onCancel={() => setModalAddOrderItemOpen(false)}
                centered
                width={540}
            >
                <AddOrderItemForm
                    order={order}
                    ref={addOrderItemFormRef}
                    onSuccess={handleOrderItemSuccess}
                />
            </Modal>
        </>
    );
}
