import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Select } from "antd";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "@redux/store";
import { setCurrentApp } from "@redux/slices/preferencesSlice";
import type { SelectProps } from "antd";

import UserActions from "../UserActions";

import Ce<PERSON><PERSON><PERSON><PERSON> from "@assets/logos/ceu-white.svg?react";
import ChartBartLine from "@assets/icons/huge/chart-bar-line.svg?react";
import Filter from "@assets/icons/huge/filter.svg?react";
import SchoolTie from "@assets/icons/huge/school-tie.svg?react";
import WebDesign from "@assets/icons/huge/web-design.svg?react";
import ArrowDown from "@assets/icons/huge/arrow-down.svg?react";
import CmsSideBar from "@components/cms/molecules/CmsSideBar";

interface AppOption {
    key: string;
    icon: React.FC<React.SVGProps<SVGSVGElement>>;
    url: string;
    label: string;
}

const APPS: AppOption[] = [
    {
        key: "erp",
        icon: ChartBartLine,
        label: "ERP",
        url: "/erp",
    },
    {
        key: "crm",
        icon: Filter,
        label: "CRM",
        url: "/crm",
    },
    {
        key: "lms",
        icon: SchoolTie,
        label: "LMS",
        url: "/lms",
    },
    {
        key: "cms",
        icon: WebDesign,
        label: "CMS",
        url: "/cms",
    },
];

const Header: React.FC = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const location = useLocation();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const { currentApp } = useSelector((state: RootState) => state.preferences);

    const getCurrentAppFromPath = useCallback(() => {
        const path = location.pathname.split("/")[1];
        return APPS.find((app) => app.url === `/${path}`);
    }, [location.pathname]);

    useEffect(() => {
        const currentPathApp = getCurrentAppFromPath();
        if (currentPathApp && currentPathApp.key !== currentApp) {
            dispatch(setCurrentApp(currentPathApp.key));
        }
    }, [currentApp, dispatch, getCurrentAppFromPath]);

    const selectedApp =
        location.pathname === "/"
            ? null
            : APPS.find((app) => app.key === currentApp) ||
              getCurrentAppFromPath() ||
              null;

    const selectOptions: SelectProps["options"] = APPS.map((app) => ({
        value: app.key,
        label: (
            <div className="flex items-center justify-start gap-4 py-1">
                <app.icon className="w-4 h-4" />
                <span className="text-black-full">{app.label}</span>
            </div>
        ),
    }));

    const handleAppChange = (value: string) => {
        const app = APPS.find((app) => app.key === value);
        if (!app) return;

        dispatch(setCurrentApp(value));
        navigate(app.url);
    };

    const toggleMobileMenu = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

    return (
        <header className="md:px-14 py-2 flex justify-between items-center bg-white-full sticky top-0 z-50 shadow-sm">
            <div className="flex items-center md:gap-12 px-2 md:px-0">
                <Button
                    className="block md:hidden"
                    type="text"
                    onClick={toggleMobileMenu}
                    aria-label="Toggle menu"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                    >
                        <line x1="4" x2="20" y1="12" y2="12" />
                        <line x1="4" x2="20" y1="6" y2="6" />
                        <line x1="4" x2="20" y1="18" y2="18" />
                    </svg>
                </Button>

                <Link to="/" className="flex items-center">
                    <CeuWhite className="h-12" aria-label="CEU Logo" />
                </Link>

                <Select
                    value={selectedApp?.key}
                    options={selectOptions}
                    onChange={handleAppChange}
                    className="min-w-[200px]"
                    dropdownStyle={{ zIndex: 1000 }}
                    placeholder="Seleccionar App"
                >
                    {selectedApp && (
                        <Select.Option className="flex justify-between items-center gap-3 py-2">
                            <div className="flex items-center gap-4">
                                <selectedApp.icon className="w-4 h-4 fill-none" />
                                <span className="text-black-full font-semibold text-sm">
                                    {selectedApp.label}
                                </span>
                            </div>
                            <ArrowDown className="w-4 h-4" />
                        </Select.Option>
                    )}
                </Select>
            </div>

            <UserActions />

            {/* Mobile Menu */}
            {isMobileMenuOpen && (
                <div className="fixed inset-0 top-16 bg-white-full md:hidden z-40">
                    <CmsSideBar />
                </div>
            )}
        </header>
    );
};

export default Header;
