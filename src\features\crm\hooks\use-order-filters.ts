import { useSearchParams } from "react-router-dom";
import { useMemo } from "react";
import { OrderStage, OrderCurrency } from "@/features/crm/types/order";
import { ListOrdersQueryParams } from "@/features/crm/services/portals/order";

export const useOrderFilters = () => {
    const [searchParams] = useSearchParams();

    const filters = useMemo((): Omit<ListOrdersQueryParams, "page" | "pageSize"> => {
        const stages = searchParams.get("stages");
        const currency = searchParams.get("currency");
        const offerings = searchParams.get("offerings");
        const startDate = searchParams.get("startDate");
        const endDate = searchParams.get("endDate");
        const search = searchParams.get("search");

        return {
            search: search || undefined,
            stages: stages ? (stages.split(",") as OrderStage[]) : undefined,
            currency:
                currency && currency !== "all"
                    ? (currency as OrderCurrency)
                    : undefined,
            offerings: offerings ? offerings.split(",") : undefined,
            startDate: startDate || undefined,
            endDate: endDate || undefined,
        };
    }, [searchParams]);

    const hasActiveFilters = useMemo(() => {
        return !!(
            filters.stages ||
            filters.currency ||
            filters.offerings?.length ||
            filters.startDate ||
            filters.endDate
        );
    }, [filters]);

    return {
        filters,
        hasActiveFilters,
    };
};
