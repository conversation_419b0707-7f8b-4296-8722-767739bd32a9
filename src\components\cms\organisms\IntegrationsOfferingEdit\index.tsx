import { Form, Input, Button, Typography, App } from "antd";
import { useNavigate } from "react-router-dom";
import { AxiosError } from "axios";
import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";

const { Text } = Typography;

import { Offering, PartialUpdateOfferingBody } from "@myTypes/offering";
import { updateOffering, deleteOffering } from "@services/portals/cms/offering";
import { openErrorNotification } from "@lib/notification";
import Save from "@assets/icons/general/save-stroke.svg?react";
import Trash from "@assets/icons/huge/trash-white.svg?react";
import { extractErrorMessages } from "@lib/error-helpers";

interface IntegrationsOfferingEditProps {
    oid: string;
    data: Offering;
    handleRefetch: () => void;
}

export default function IntegrationsOfferingEdit({
    oid,
    data,
    handleRefetch,
}: IntegrationsOfferingEditProps) {
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [hasChanges, setHasChanges] = useState(false);

    const { message, notification } = App.useApp();

    // Mutation para actualizar la offering
    const updateOfferingMutation = useMutation({
        mutationFn: (body: PartialUpdateOfferingBody) => updateOffering(oid, body),
        onSuccess: () => {
            message.success("Integración actualizada correctamente");
            setHasChanges(false);
            handleRefetch();
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = extractErrorMessages(
                error.response?.data || {},
            ).filter((msg) => typeof msg === "string");

            openErrorNotification(
                "Error al actualizar la integración",
                errorMessages.length
                    ? errorMessages
                    : "Error al actualizar la integración",
                notification,
            );
        },
    });

    // Mutation para eliminar la offering
    const deleteOfferingMutation = useMutation({
        mutationFn: () => deleteOffering(oid),
        onSuccess: () => {
            message.success("Producto eliminado correctamente");
            navigate("/cms/offering");
        },
        onError: (error: AxiosError) => {
            const errorMessages: string[] = extractErrorMessages(
                error.response?.data || {},
            ).filter((msg) => typeof msg === "string");
            openErrorNotification(
                "Error al eliminar el producto",
                errorMessages.length ? errorMessages : "Error al eliminar el producto",
                notification,
            );
        },
    });

    // Establecer valores iniciales del formulario
    useEffect(() => {
        form.setFieldsValue({
            extReference: data.extReference || "",
        });
    }, [data, form]);

    // Detectar cambios en el formulario
    const handleFormChange = () => {
        setHasChanges(true);
    };

    // Manejar envío del formulario
    const handleSubmit = (values: { extReference: string }) => {
        const body: PartialUpdateOfferingBody = {
            extReference: values.extReference || null,
        };

        updateOfferingMutation.mutate(body);
    };

    // Manejar eliminación
    const handleDelete = () => {
        deleteOfferingMutation.mutate();
    };

    return (
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
            <div className="bg-white-full col-span-1 lg:col-span-4 p-5 rounded-lg shadow-sm">
                <div className="flex justify-between items-center mb-6">
                    <p className="text-gray-400 font-semibold text-sm">INTEGRACIONES</p>
                </div>

                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                    onValuesChange={handleFormChange}
                >
                    <div className="space-y-4">
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <Text className="text-blue-800">
                                <strong>Información:</strong> Conecta este curso con
                                Google Classroom ingresando el código de clase
                                correspondiente. Esto permitirá la integración
                                automática de estudiantes cuando realicen una compra.
                            </Text>
                        </div>

                        <Form.Item
                            name="extReference"
                            label={
                                <span className="text-gray-700 font-medium">
                                    Código de clase de Google Classroom
                                </span>
                            }
                            help="Ingresa el código único de la clase en Google Classroom"
                        >
                            <Input
                                placeholder="Ej: abc123def"
                                size="large"
                                maxLength={50}
                                showCount
                                className="w-full"
                            />
                        </Form.Item>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <Text type="secondary" className="text-sm">
                                <strong>¿Cómo obtener el código de clase?</strong>
                                <br />
                                1. Ve a Google Classroom y selecciona tu clase
                                <br />
                                2. Haz clic en "Configuración" (ícono de engranaje)
                                <br />
                                3. Copia el "Código de la clase" que aparece en la
                                sección General
                            </Text>
                        </div>
                    </div>
                </Form>
            </div>

            <div className="col-span-1 lg:col-span-2 space-y-6">
                <div className="bg-white-full p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm mb-4">ACCIONES</p>
                    <div className="flex flex-row gap-3 justify-end">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Trash />}
                            danger
                            loading={deleteOfferingMutation.isPending}
                            onClick={handleDelete}
                        >
                            Eliminar
                        </Button>

                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Save />}
                            loading={updateOfferingMutation.isPending}
                            disabled={!hasChanges}
                            onClick={() => form.submit()}
                        >
                            Guardar
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}
