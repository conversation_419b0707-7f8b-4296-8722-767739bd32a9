import { Dayjs } from "dayjs";
import { EventModality, EventStage } from "./event";

type AuditBaseType = {
    createdAt: string;
    updatedAt: string;
};

type Instructor = {
    name: string;
    iid: string;
};

export type EventSchedulePartnership = {
    key: string;
    pid: string;
    name: string;
    institution: string;
} & AuditBaseType;

export type FileBase = {
    fid: string;
    name: string;
    url: string;
    contentType: string;
};

export type EventScheduleCoverImage = FileBase;
export type EventScheduleThumbnail = FileBase;

export type PartnershipEnrollmentUrl = {
    pid: string;
    name: string;
    institution: string;
    enrollmentUrl: string;
};

export type EventSchedule = {
    key: string;
    esid: string;
    event: string;
    name: string;
    isGeneral: boolean;
    description?: string;
    thumbnail: EventScheduleThumbnail;
    coverImage: EventScheduleCoverImage;
    startDate: string;
    endDate: string;
    stage: EventStage;
    modality: EventModality;
    location: string;
    instructor?: Instructor;
    price: number;
    partnerships: EventSchedulePartnership[];
    enrollmentUrl: string;
    partnershipEnrollmentUrls: PartnershipEnrollmentUrl[];
    extEventLink?: string;
} & AuditBaseType;

export type CreateEventScheduleFormBody = {
    event: string;
    startDate: string;
    endDate: string;
    partnerships: string[];
    stage: EventStage;
    isGeneral: boolean;
};

export type CreateEventScheduleFormValues = CreateEventScheduleFormBody & {
    completeInfo: boolean;
};

export type UpdateEventScheduleFormValues = {
    name: string;
    modality: string;
    stage: EventStage;
    partnerships: string[];
    description: string;
    rangeDate: [Dayjs, Dayjs];
    location: string;
    instructor: string;
    price: number;
    isGeneral: boolean;
};

export const DEFAULT_PAGE = 1;
export const DEFAULT_PAGE_SIZE = 10;

export type ListEventSchedulesQueryParams = {
    page?: number;
    pageSize?: number;
    search?: string;
    event?: string;
};
