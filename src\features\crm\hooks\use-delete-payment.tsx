import { useMutation, useQueryClient } from "@tanstack/react-query";
import { App, Modal } from "antd";
import { deletePayment } from "../services/portals/payment";
import { AlertTriangle } from "lucide-react";

export const useDeletePayment = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();
    const { confirm } = Modal;

    const deleteMutation = useMutation({
        mutationFn: (pid: string) => deletePayment(pid),
        onSuccess: () => {
            notification.success({
                message: "Pago eliminado",
                description: "El pago se ha eliminado correctamente",
                duration: 3,
            });
            queryClient.invalidateQueries({ queryKey: ["payments"] });
        },
        onError: (error: Error) => {
            notification.error({
                message: "Error al eliminar el pago",
                description: error.message || "Ha ocurrido un error inesperado",
                duration: 5,
            });
        },
    });

    const confirmDelete = (pid: string) => {
        confirm({
            title: "¿Estás seguro de eliminar este pago?",
            icon: <AlertTriangle className="text-orange-500" />,
            content:
                "Esta acción no se puede deshacer. El pago será eliminado permanentemente.",
            okText: "Sí, eliminar",
            okType: "danger",
            cancelText: "Cancelar",
            onOk() {
                deleteMutation.mutate(pid);
            },
        });
    };

    return {
        deletePayment: confirmDelete,
        isLoading: deleteMutation.isPending,
    };
};
