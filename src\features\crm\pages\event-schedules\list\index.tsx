import CrmLayout from "@/features/crm/layout";
import WelcomeBar from "@components/shared/molecules/WelcomeBar";
import { Bad<PERSON>, Button, Drawer, Input, Modal, Pagination, Typography } from "antd";
import { Plus, SlidersHorizontal } from "lucide-react";
import { useState } from "react";

import CreateEventScheduleForm from "@/features/crm/components/organisms/create-event-schedule-form";
import { useEventSchedules } from "@/features/crm/hooks/use-event-schedule";
import EventSchedulesTable from "@/features/crm/components/organisms/event-schedules-table";
import { useSearchParams } from "react-router-dom";
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    ListEventSchedulesQueryParams,
} from "@/features/crm/types/event-schedule";

const { Text } = Typography;
const { Search } = Input;

export default function EventSchedulesListPage() {
    const [searchParams, setSearchParams] = useSearchParams();
    const page = Number(searchParams.get("page")) || DEFAULT_PAGE;
    const pageSize = Number(searchParams.get("pageSize")) || DEFAULT_PAGE_SIZE;
    const search = searchParams.get("search") || undefined;

    const queryParams: ListEventSchedulesQueryParams = {
        page,
        pageSize,
        search,
    };

    const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState<boolean>(false);
    const [activeTag, setActiveTag] = useState("Todos");
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const tags = ["Todos", "Culminados", "Planeados"];

    const { count, eventSchedules } = useEventSchedules({ queryParams });

    const handleSetPage = (page: number, pageSize: number) => {
        setSearchParams((prev) => {
            prev.set("page", page.toString());
            prev.set("pageSize", pageSize.toString());
            return prev;
        });
    };

    const handleSetSerachQuery = (value: string) => {
        setSearchParams((prev) => {
            prev.set("search", value);
            return prev;
        });
    };

    return (
        <CrmLayout>
            <div className="w-full h-full space-y-5">
                <div className="flex justify-between items-center">
                    <WelcomeBar helperText="Gestiona aquí los horarios de eventos" />
                    <div className="flex gap-3">
                        <Button
                            type="primary"
                            size="large"
                            style={{ fontSize: 16 }}
                            icon={<Plus />}
                            onClick={() => setIsCreateModalOpen(true)}
                        >
                            Agregar
                        </Button>
                        <Modal
                            centered
                            open={isCreateModalOpen}
                            onCancel={() => setIsCreateModalOpen(false)}
                            footer={false}
                            title={
                                <div className="w-full flex justify-center text-xl py-1">
                                    Agregar nuevo Horario
                                </div>
                            }
                        >
                            <CreateEventScheduleForm
                                closeModal={() => setIsCreateModalOpen(false)}
                            />
                        </Modal>
                    </div>
                </div>
                <div className="p-5 bg-white-full rounded-lg space-y-5">
                    <div className="flex flex-col lg:flex-row justify-between items-center">
                        <Text className="text-black-medium text-2xl font-semibold">
                            Horarios de Eventos{" "}
                            <Badge count={count} color="blue" size="default" />
                        </Text>
                        <Search
                            size="large"
                            placeholder="Buscar por nombre de evento"
                            onSearch={(value) => {
                                handleSetSerachQuery(value);
                            }}
                            enterButton
                            allowClear
                            className="max-w-screen-sm"
                        />
                        <div className="flex items-center gap-3">
                            <Button
                                icon={<SlidersHorizontal size={16} />}
                                onClick={() => setIsFilterDrawerOpen(true)}
                            >
                                Filtros
                            </Button>
                            <Drawer
                                title="Aplicar filtros"
                                placement="right"
                                closable={true}
                                onClose={() => setIsFilterDrawerOpen(false)}
                                open={isFilterDrawerOpen}
                            >
                                {/* TODO: Add filter form */}
                            </Drawer>
                        </div>
                    </div>
                </div>

                <div className="hidden items-center gap-2 p-3 bg-gray-50 rounded-lg shadow-sm">
                    {tags.map((tag) => (
                        <button
                            key={tag}
                            onClick={() => setActiveTag(tag)}
                            className={`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
                                activeTag === tag
                                    ? "bg-blue-500 text-white-full shadow-md"
                                    : "bg-white text-gray-600 hover:bg-gray-100"
                            }`}
                        >
                            {tag}
                        </button>
                    ))}
                </div>
                <EventSchedulesTable eventSchedules={eventSchedules} />
                <div className="flex justify-between items-center p-4 bg-white-full rounded-lg shadow-sm">
                    <Text type="secondary">
                        {eventSchedules.length} de {count} Horarios de Eventos
                    </Text>
                    <Pagination
                        current={page}
                        pageSize={pageSize}
                        total={count}
                        onChange={handleSetPage}
                        showSizeChanger
                    />
                </div>
            </div>
        </CrmLayout>
    );
};