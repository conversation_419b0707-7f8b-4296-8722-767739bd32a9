import { useState, useMemo } from "react";
import { Row, Col, Card, Statistic, Table, Tag, Tooltip } from "antd";
import {
    DollarSign,
    CreditCard,
    BarChart2,
    Activity,
    TrendingUp,
    Percent,
    Clock,
    Users,
    CheckCircle,
    AlertTriangle,
} from "lucide-react";

// Components
import StatCard from "@/features/crm/components/atoms/stat-card";
import PieChartCard from "@/features/crm/components/molecules/pie-chart-card";
import BarChartCard from "@/features/crm/components/molecules/bar-chart-card";
import PaymentFilterCard, {
    PaymentFiltersState,
} from "@/features/crm/components/molecules/payment-filter-card";

// Mock data
import {
    mockPayments,
    paidPayments,
    totalPaidPEN,
    totalPaidUSD,
    totalPendingPEN,
    totalPendingUSD,
    paymentsByMonth,
    paymentsByMethod,
    paymentMethods,
    paymentsByStage,
    paymentEfficiency,
    topCustomers,
    paymentConversionMetrics,
    paymentsByCurrency,
} from "@/features/crm/mock/payment-data";
import { PaymentCurrency, PaymentListItem } from "@/features/crm/types/payment";

export default function PaymentsDashboardTab() {
    const [filters, setFilters] = useState<PaymentFiltersState>({
        dateRange: null,
        paymentStatus: "all",
        currency: "all",
        paymentMethod: "all",
    });

    // Filtramos los pagos en base a los filtros seleccionados
    const filteredPayments = useMemo(() => {
        let result = [...mockPayments];

        // Filtro por estado de pago
        if (filters.paymentStatus === "paid") {
            result = result.filter((payment) => payment.isPaid);
        } else if (filters.paymentStatus === "pending") {
            result = result.filter((payment) => !payment.isPaid);
        }

        // Filtro por moneda
        if (filters.currency === "pen") {
            result = result.filter(
                (payment) => payment.currency === PaymentCurrency.PEN,
            );
        } else if (filters.currency === "usd") {
            result = result.filter(
                (payment) => payment.currency === PaymentCurrency.USD,
            );
        }

        // Filtro por método de pago
        if (filters.paymentMethod !== "all") {
            result = result.filter(
                (payment) => payment.paymentMethod.pmid === filters.paymentMethod,
            );
        }

        // Filtro por fecha (si se aplica)
        if (
            filters.dateRange &&
            Array.isArray(filters.dateRange) &&
            filters.dateRange.length === 2 &&
            filters.dateRange[0] &&
            filters.dateRange[1]
        ) {
            // Convertir de dayjs a Date si es necesario
            const startDate = new Date(filters.dateRange[0].toString());
            startDate.setHours(0, 0, 0, 0);

            const endDate = new Date(filters.dateRange[1].toString());
            endDate.setHours(23, 59, 59, 999);

            result = result.filter((payment) => {
                const paymentDate = new Date(payment.paymentDate);
                return paymentDate >= startDate && paymentDate <= endDate;
            });
        }

        return result;
    }, [filters]);

    // Columnas para la tabla de pagos recientes
    const paymentColumns = [
        {
            title: "CLIENTE",
            dataIndex: ["order", "owner", "fullName"],
            key: "customer",
            render: (text: string) => <span className="font-medium">{text}</span>,
        },
        {
            title: "FECHA",
            dataIndex: "paymentDate",
            key: "date",
            render: (date: string) => new Date(date).toLocaleDateString(),
        },
        {
            title: "MÉTODO",
            dataIndex: ["paymentMethod", "name"],
            key: "method",
            render: (text: string) => <Tag color="blue">{text}</Tag>,
        },
        {
            title: "MONTO",
            key: "amount",
            render: (record: PaymentListItem) => (
                <div className="flex items-center">
                    <DollarSign size={16} className="text-green-600 mr-1" />
                    <span className="font-medium">
                        {record.currency === PaymentCurrency.USD ? "$" : "S/. "}
                        {record.amount.toLocaleString()}
                    </span>
                </div>
            ),
        },
        {
            title: "ESTADO",
            dataIndex: "isPaid",
            key: "status",
            render: (isPaid: boolean) => (
                <Tag color={isPaid ? "success" : "warning"}>
                    {isPaid ? "Pagado" : "Pendiente"}
                </Tag>
            ),
        },
    ];

    // Calcular KPIs adicionales
    const paidCount = paidPayments.length;
    const totalPayments = mockPayments.length;
    const paymentSuccessRate = Math.round((paidCount / totalPayments) * 100);

    return (
        <div>
            {/* Filtros */}
            <PaymentFilterCard
                paymentMethods={paymentMethods}
                filters={filters}
                onFilterChange={setFilters}
                onReset={() =>
                    setFilters({
                        dateRange: null,
                        paymentStatus: "all",
                        currency: "all",
                        paymentMethod: "all",
                    })
                }
            />

            {/* Stats Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Total Pagado (PEN)"
                        value={`S/. ${totalPaidPEN.toLocaleString()}`}
                        icon={<DollarSign size={24} className="text-green-500" />}
                        color="#73d13d"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Total Pagado (USD)"
                        value={`$ ${totalPaidUSD.toLocaleString()}`}
                        icon={<DollarSign size={24} className="text-blue-500" />}
                        color="#4096ff"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Por Cobrar (PEN)"
                        value={`S/. ${totalPendingPEN.toLocaleString()}`}
                        icon={<AlertTriangle size={24} className="text-yellow-500" />}
                        color="#faad14"
                    />
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <StatCard
                        title="Por Cobrar (USD)"
                        value={`$ ${totalPendingUSD.toLocaleString()}`}
                        icon={<AlertTriangle size={24} className="text-orange-500" />}
                        color="#fa8c16"
                    />
                </Col>
            </Row>

            {/* KPI Cards */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} sm={12} lg={6}>
                    <Card>
                        <Statistic
                            title={
                                <span className="text-gray-600 font-medium">
                                    Tasa de pagos exitosos
                                </span>
                            }
                            value={paymentSuccessRate}
                            suffix="%"
                            precision={1}
                            valueStyle={{ color: "#3f8600" }}
                            prefix={
                                <CheckCircle
                                    size={20}
                                    className="mr-2 text-green-500"
                                />
                            }
                        />
                        <div className="text-xs text-gray-500 mt-2">
                            {paidCount} de {totalPayments} pagos completados
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <Card>
                        <Statistic
                            title={
                                <span className="text-gray-600 font-medium">
                                    Promedio días para pagar
                                </span>
                            }
                            value={paymentConversionMetrics.averageDaysToPay}
                            precision={1}
                            valueStyle={{ color: "#1890ff" }}
                            prefix={<Clock size={20} className="mr-2 text-blue-500" />}
                            suffix="días"
                        />
                        <div className="text-xs text-gray-500 mt-2">
                            Desde la creación hasta el pago
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <Card>
                        <Statistic
                            title={
                                <span className="text-gray-600 font-medium">
                                    Conversión de pagos
                                </span>
                            }
                            value={paymentConversionMetrics.conversionRate}
                            suffix="%"
                            precision={1}
                            valueStyle={{ color: "#722ed1" }}
                            prefix={
                                <Percent size={20} className="mr-2 text-purple-500" />
                            }
                        />
                        <div className="text-xs text-gray-500 mt-2">
                            De "Por pagar" a "Pagado"
                        </div>
                    </Card>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                    <Card>
                        <Statistic
                            title={
                                <span className="text-gray-600 font-medium">
                                    Tasa de recuperación
                                </span>
                            }
                            value={paymentConversionMetrics.paymentRecoveryRate}
                            suffix="%"
                            precision={1}
                            valueStyle={{ color: "#fa8c16" }}
                            prefix={
                                <TrendingUp
                                    size={20}
                                    className="mr-2 text-orange-500"
                                />
                            }
                        />
                        <div className="text-xs text-gray-500 mt-2">
                            Pagos recuperados tras seguimiento
                        </div>
                    </Card>
                </Col>
            </Row>

            {/* Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={16}>
                    <BarChartCard
                        title="Pagos por mes"
                        data={paymentsByMonth}
                        dataKey="paid"
                        xAxisDataKey="month"
                        secondaryDataKey="pending"
                        barColors={["#73d13d", "#faad14"]}
                        icon={<BarChart2 className="h-5 w-5 text-blue-500" />}
                        formatter={(value) => `S/. ${value.toLocaleString()}`}
                        legend={[
                            { name: "Pagados", color: "#73d13d" },
                            { name: "Pendientes", color: "#faad14" },
                        ]}
                    />
                </Col>
                <Col xs={24} lg={8}>
                    <PieChartCard
                        title="Distribución por método de pago"
                        data={paymentsByMethod.map((method) => ({
                            name: method.name,
                            value: method.value,
                        }))}
                        colors={["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"]}
                        icon={<CreditCard className="h-5 w-5 text-blue-500" />}
                    />
                </Col>
            </Row>

            {/* Additional Charts Row */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Pagos por etapa del pedido"
                        data={paymentsByStage.map((stage) => ({
                            name: stage.name,
                            value: stage.value,
                        }))}
                        colors={["#4096ff", "#36cfc9", "#faad14", "#73d13d", "#ff4d4f"]}
                        icon={<Activity className="h-5 w-5 text-purple-500" />}
                    />
                </Col>
                <Col xs={24} lg={12}>
                    <PieChartCard
                        title="Distribución por moneda"
                        data={paymentsByCurrency}
                        colors={["#73d13d", "#4096ff"]}
                        icon={<DollarSign className="h-5 w-5 text-green-500" />}
                    />
                </Col>
            </Row>

            {/* Top Customers */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <Users className="mr-2 h-5 w-5 text-blue-500" />
                                <span>Top Clientes por Monto</span>
                            </div>
                        }
                    >
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            {topCustomers.map((customer, index) => (
                                <Card className="bg-gray-50" key={customer.uid}>
                                    <div className="flex items-center mb-2">
                                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold">
                                            {index + 1}
                                        </div>
                                        <div className="ml-2">
                                            <div className="font-medium">
                                                {customer.name}
                                            </div>
                                            <div className="text-xs text-gray-500">
                                                {customer.paymentsCount} pagos
                                            </div>
                                        </div>
                                    </div>
                                    <Statistic
                                        value={customer.totalAmount}
                                        precision={0}
                                        valueStyle={{ color: "#3f8600" }}
                                        prefix="S/. "
                                        formatter={(value) =>
                                            `${(+value).toLocaleString()}`
                                        }
                                    />
                                </Card>
                            ))}
                        </div>
                    </Card>
                </Col>
            </Row>

            {/* Payment Efficiency */}
            <Row gutter={[16, 16]} className="mb-6">
                <Col xs={24}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <Clock className="mr-2 h-5 w-5 text-purple-500" />
                                <span>Pagos más rápidos</span>
                            </div>
                        }
                    >
                        <Table
                            dataSource={paymentEfficiency}
                            columns={[
                                {
                                    title: "Cliente",
                                    dataIndex: "customerName",
                                    key: "customerName",
                                },
                                {
                                    title: "Creación",
                                    dataIndex: "createdAt",
                                    key: "createdAt",
                                    render: (date) =>
                                        new Date(date).toLocaleDateString(),
                                },
                                {
                                    title: "Fecha de pago",
                                    dataIndex: "paymentDate",
                                    key: "paymentDate",
                                    render: (date) =>
                                        new Date(date).toLocaleDateString(),
                                },
                                {
                                    title: "Tiempo",
                                    dataIndex: "daysToPay",
                                    key: "daysToPay",
                                    render: (days) => (
                                        <Tooltip title="Días desde creación hasta pago">
                                            <Tag
                                                color={
                                                    days <= 1
                                                        ? "success"
                                                        : days <= 3
                                                          ? "blue"
                                                          : "orange"
                                                }
                                            >
                                                {days} {days === 1 ? "día" : "días"}
                                            </Tag>
                                        </Tooltip>
                                    ),
                                },
                                {
                                    title: "Monto",
                                    key: "amount",
                                    render: (record) => (
                                        <span className="font-medium">
                                            {record.currency === PaymentCurrency.USD
                                                ? "$"
                                                : "S/. "}
                                            {record.amount.toLocaleString()}
                                        </span>
                                    ),
                                },
                            ]}
                            pagination={false}
                        />
                    </Card>
                </Col>
            </Row>

            {/* Recent Payments */}
            <Row gutter={[16, 16]}>
                <Col xs={24}>
                    <Card
                        title={
                            <div className="flex items-center">
                                <Activity className="mr-2 h-5 w-5 text-blue-500" />
                                <span>Pagos recientes</span>
                            </div>
                        }
                        className="mb-6"
                    >
                        <Table
                            dataSource={filteredPayments.slice(0, 10)}
                            columns={paymentColumns}
                            pagination={false}
                        />
                    </Card>
                </Col>
            </Row>
        </div>
    );
}
