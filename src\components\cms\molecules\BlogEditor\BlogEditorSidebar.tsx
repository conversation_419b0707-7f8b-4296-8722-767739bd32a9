import React, { useState, useEffect } from "react";
import {
    <PERSON>po<PERSON>,
    Divider,
    Badge,
    Space,
    List,
    Button,
    Empty,
    message,
    Popover,
} from "antd";
import { Link, useNavigate, useParams, useSearchParams } from "react-router-dom";
import CeuWhite from "@assets/logos/ceu-white.svg?react";
import {
    ArchiveIcon,
    ArrowLeftFromLineIcon,
    FileCheckIcon,
    FileClockIcon,
    FilePlus2Icon,
    ChevronRightIcon,
    ChevronDownIcon,
    MoreVertical,
} from "lucide-react";
import { BlogPost, BlogStatus } from "@myTypes/blog";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import {
    createBlogPost,
    deleteBlogPost,
    getBlogPosts,
} from "@services/portals/cms/blogs/post";
import SearchText from "@components/shared/atoms/SearchText";
import { onErrorMessage, onSuccessMessage } from "@lib/message";
import type { AxiosError } from "axios";
import { DeleteConfirm } from "@components/shared/atoms/DeleteConfirm";
import DeleteStroke from "@assets/icons/huge/delete-stroke.svg?react";

const { Text } = Typography;

const DEFAULT_PAGE_SIZE = 5;

interface BlogCategory {
    key: BlogStatus;
    title: string;
    icon: React.ReactNode;
    expanded: boolean;
}

const BlogEditorSidebar = () => {
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const { bid } = useParams<{ bid: string }>();
    const [searchParams] = useSearchParams();
    const queryClient = useQueryClient();
    const [currentStatus] = useState(
        searchParams.get("status") || BlogStatus.PUBLISHED,
    );

    const [categories, setCategories] = useState<BlogCategory[]>([
        {
            key: BlogStatus.PUBLISHED,
            title: "PUBLICADOS",
            icon: <FileCheckIcon size={16} />,
            expanded: currentStatus === BlogStatus.PUBLISHED,
        },
        {
            key: BlogStatus.DRAFT,
            title: "MIS BORRADORES",
            icon: <FileClockIcon size={16} />,
            expanded: currentStatus === BlogStatus.DRAFT,
        },
        {
            key: BlogStatus.ARCHIVED,
            title: "ARCHIVADOS",
            icon: <ArchiveIcon size={16} />,
            expanded: currentStatus === BlogStatus.ARCHIVED,
        },
    ]);

    // Estado para almacenar la página actual por categoría
    const [paginationState, setPaginationState] = useState<
        Record<
            BlogStatus,
            {
                page: number;
                pageSize: number;
            }
        >
    >({
        [BlogStatus.PUBLISHED]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
        [BlogStatus.DRAFT]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
        [BlogStatus.ARCHIVED]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
    });

    // Queries para cada estado de blog
    const publishedQuery = useQuery({
        queryKey: [
            "blogs",
            BlogStatus.PUBLISHED,
            paginationState[BlogStatus.PUBLISHED].pageSize,
            searchParams.get("search"),
        ],
        queryFn: () =>
            getBlogPosts({
                pageSize: paginationState[BlogStatus.PUBLISHED].pageSize,
                filters: {
                    status: BlogStatus.PUBLISHED,
                    search: searchParams.get("search") || undefined || undefined,
                },
            }),
        enabled:
            categories.find((c) => c.key === BlogStatus.PUBLISHED)?.expanded || false,
    });

    const draftsQuery = useQuery({
        queryKey: [
            "blogs",
            BlogStatus.DRAFT,
            paginationState[BlogStatus.DRAFT].pageSize,
            searchParams.get("search"),
        ],
        queryFn: () =>
            getBlogPosts({
                pageSize: paginationState[BlogStatus.DRAFT].pageSize,
                filters: {
                    status: BlogStatus.DRAFT,
                    search: searchParams.get("search") || undefined,
                },
            }),
        enabled: categories.find((c) => c.key === BlogStatus.DRAFT)?.expanded || false,
    });

    const archivedQuery = useQuery({
        queryKey: [
            "blogs",
            BlogStatus.ARCHIVED,
            paginationState[BlogStatus.ARCHIVED].pageSize,
            searchParams.get("search"),
        ],
        queryFn: () =>
            getBlogPosts({
                pageSize: paginationState[BlogStatus.ARCHIVED].pageSize,
                filters: {
                    status: BlogStatus.ARCHIVED,
                    search: searchParams.get("search") || undefined,
                },
            }),
        enabled:
            categories.find((c) => c.key === BlogStatus.ARCHIVED)?.expanded || false,
    });

    // Mutación para crear un nuevo borrador
    const createDraftMutation = useMutation({
        mutationFn: createBlogPost,
        onSuccess: (data) => {
            navigate(`/cms/blog/${data.bid}/edit`);
            // Invalidar la caché de borradores para que se actualice la lista
            queryClient.invalidateQueries({ queryKey: ["blogs", BlogStatus.DRAFT] });
        },
        onError: (error) => {
            console.error("Error creating new draft:", error);
        },
    });

    // Efecto para resetear la paginación cuando cambia el término de búsqueda
    useEffect(() => {
        setPaginationState({
            [BlogStatus.PUBLISHED]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
            [BlogStatus.DRAFT]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
            [BlogStatus.ARCHIVED]: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
        });

        // Invalidar queries para forzar recarga con el nuevo término de búsqueda
        queryClient.invalidateQueries({ queryKey: ["blogs"] });
    }, [searchParams, queryClient]);

    const toggleCategory = (key: BlogStatus) => {
        setCategories(
            categories.map((category) => {
                if (category.key === key) {
                    return { ...category, expanded: !category.expanded };
                }
                return category;
            }),
        );
    };

    const handleLoadMore = (status: BlogStatus) => {
        setPaginationState((prev) => ({
            ...prev,
            [status]: {
                ...prev[status],
                pageSize: prev[status].pageSize + DEFAULT_PAGE_SIZE,
            },
        }));

        // Invalidar la query para forzar una recarga con el nuevo pageSize
        queryClient.invalidateQueries({
            queryKey: ["blogs", status],
        });
    };

    const handleBackToBlogList = () => {
        navigate("/cms/blog");
    };

    const handleCreateNewDraft = () => {
        createDraftMutation.mutate();
    };

    const handleBlogClick = (bid: string, status: BlogStatus) => {
        navigate(`/cms/blog/${bid}/edit?status=${status}`);
    };

    // Preparar datos para renderizado
    const getCategoryData = (status: BlogStatus) => {
        const query =
            status === BlogStatus.PUBLISHED
                ? publishedQuery
                : status === BlogStatus.DRAFT
                  ? draftsQuery
                  : archivedQuery;

        // Filtrar duplicados en los resultados
        const uniqueResults =
            query.data?.results.filter(
                (post, index, self) =>
                    index === self.findIndex((p) => p.bid === post.bid),
            ) || [];

        return {
            data: uniqueResults,
            total: query.data?.count || 0,
            loading: query.isLoading || query.isFetching,
            hasMore: !!query.data?.next,
        };
    };

    // mutations
    const deleteMutation = useMutation({
        mutationFn: (bid: string) => deleteBlogPost(bid),
        onSuccess: () => {
            onSuccessMessage("Blog eliminado exitosamente", messageApi);
            queryClient.invalidateQueries({ queryKey: ["blog", bid] });
            navigate(`/cms/blog/edit`, { replace: true });
        },
        onError: (error: AxiosError) => {
            onErrorMessage(error.message, messageApi);
        },
    });

    return (
        <>
            {contextHolder}
            <div className="h-full flex flex-col p-4 z-0 fixed top-0 bottom-0 left-0 w-[290px]">
                <div className="mb-8 flex justify-center">
                    <Link to="/" className="flex items-center">
                        <CeuWhite className="h-12" aria-label="CEU Logo" />
                    </Link>
                </div>

                <div className="mb-4">
                    <SearchText />
                </div>

                <div
                    className="flex items-center p-3 rounded-md mb-2 bg-blue-50 text-blue-500 cursor-pointer"
                    onClick={handleCreateNewDraft}
                >
                    <FilePlus2Icon size={16} className="mr-2" />
                    <Text strong>Nuevo borrador</Text>
                    {createDraftMutation.isPending && (
                        <div className="ml-2 animate-spin">
                            <svg className="h-4 w-4" viewBox="0 0 24 24">
                                <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                    fill="none"
                                />
                                <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                />
                            </svg>
                        </div>
                    )}
                </div>

                <Divider className="my-3" />

                <div className="flex-1 overflow-y-auto">
                    {categories.map((category) => {
                        const groupData = getCategoryData(category.key);

                        return (
                            <div key={category.key} className="mb-2">
                                <div
                                    onClick={() => toggleCategory(category.key)}
                                    className="flex items-center justify-between py-2 cursor-pointer"
                                >
                                    <Space>
                                        {React.cloneElement(
                                            category.icon as React.ReactElement,
                                            { className: "text-gray-600" },
                                        )}
                                        <Text
                                            type="secondary"
                                            className="text-xs font-medium tracking-wider"
                                        >
                                            {category.title}
                                        </Text>
                                    </Space>
                                    <Space>
                                        <Badge
                                            count={groupData.total}
                                            size="small"
                                            style={{
                                                backgroundColor: "blue",
                                                fontSize: "10px",
                                            }}
                                        />
                                        {category.expanded ? (
                                            <ChevronDownIcon
                                                size={14}
                                                className="text-gray-500"
                                            />
                                        ) : (
                                            <ChevronRightIcon
                                                size={14}
                                                className="text-gray-500"
                                            />
                                        )}
                                    </Space>
                                </div>

                                {category.expanded && (
                                    <div className="ml-6">
                                        <List
                                            dataSource={groupData.data}
                                            locale={{
                                                emptyText: (
                                                    <Empty
                                                        description="No hay posts"
                                                        image={
                                                            Empty.PRESENTED_IMAGE_SIMPLE
                                                        }
                                                    />
                                                ),
                                            }}
                                            renderItem={(blog: BlogPost) => (
                                                <List.Item
                                                    // activo
                                                    key={blog.bid}
                                                    className={`relative py-1 px-1.5 cursor-pointer rounded hover:bg-gray-50 border-0 ${blog.bid === bid ? "bg-blue-50" : ""}`}
                                                    onClick={() =>
                                                        handleBlogClick(
                                                            blog.bid,
                                                            blog.status,
                                                        )
                                                    }
                                                >
                                                    <Text className="px-2 text-sm block whitespace-nowrap overflow-hidden overflow-ellipsis">
                                                        {blog.title || "Sin título"}
                                                    </Text>
                                                    <div className="absolute right-0">
                                                        {/* desplegable de acciones */}
                                                        <Popover
                                                            content={
                                                                <div className="flex flex-col gap-1">
                                                                    <DeleteConfirm
                                                                        title="Eliminar blog"
                                                                        description="¿Estás seguro de eliminar el blog?"
                                                                        onConfirm={() =>
                                                                            deleteMutation.mutateAsync(
                                                                                blog.bid,
                                                                            )
                                                                        }
                                                                        customTrigger={
                                                                            <Button
                                                                                type="text"
                                                                                className="flex items-center gap-2 text-state-red-full"
                                                                            >
                                                                                <DeleteStroke className="w-5 h-5" />{" "}
                                                                                Eliminar
                                                                            </Button>
                                                                        }
                                                                    />
                                                                </div>
                                                            }
                                                            trigger={["click"]}
                                                            placement="bottomRight"
                                                        >
                                                            <Button
                                                                icon={
                                                                    <MoreVertical className="w-5 h-5" />
                                                                }
                                                                type="text"
                                                                size="small"
                                                            />
                                                        </Popover>
                                                    </div>
                                                </List.Item>
                                            )}
                                        />
                                        {groupData.hasMore && (
                                            <div className="text-center my-2">
                                                <Button
                                                    type="link"
                                                    size="small"
                                                    loading={groupData.loading}
                                                    onClick={() =>
                                                        handleLoadMore(category.key)
                                                    }
                                                    disabled={groupData.loading}
                                                >
                                                    Cargar más
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>

                <Divider className="my-3" />

                <div
                    onClick={handleBackToBlogList}
                    className="flex items-center py-2.5 cursor-pointer text-blue-500 fixed bottom-0"
                >
                    <ArrowLeftFromLineIcon size={16} className="mr-2" />
                    <Text>Ir atrás</Text>
                </div>
            </div>
        </>
    );
};

export default BlogEditorSidebar;
