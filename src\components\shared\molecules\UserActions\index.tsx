import { Dropdown } from "antd";
import type { MenuProps } from "antd";
import { Link } from "react-router-dom";

import Settings from "@assets/icons/huge/settings.svg?react";
import Support from "@assets/icons/huge/support.svg?react";
import Logout from "@assets/icons/huge/logout.svg?react";

import ArrowDown from "@assets/icons/general/arrow-down.svg?react";
import { useAuthStore } from "@store/authStore";

interface MenuItem {
    key: string;
    label?: string;
    link: string;
    icon: React.FC;
    type?: "divider";
}

const MENU_ITEMS: MenuItem[] = [
    {
        key: "divider1",
        type: "divider",
        link: "",
        icon: (() => null) as unknown as React.FC,
    },
    {
        key: "settings",
        label: "Configuración",
        link: "/settings",
        icon: Settings,
    },
    {
        key: "support",
        label: "Ayuda y Soporte",
        link: "/support",
        icon: Support,
    },
    {
        key: "logout",
        label: "Salir",
        link: "/logout",
        icon: Logout,
    },
];

export default function UserActions() {
    const { user, logout, isHydrated } = useAuthStore((state) => state);

    const dropdownItems: MenuProps["items"] = MENU_ITEMS.map((item) =>
        item.type === "divider"
            ? {
                  key: item.key,
                  type: "divider",
                  style: { margin: 0, padding: 0 },
              }
            : {
                  key: item.key,
                  label: (
                      <Link
                          to={item.link}
                          className="flex items-center gap-4 py-2 px-4 hover:bg-blue-low hover:text-blue-full fill-blue-full text-black-full"
                      >
                          <item.icon />
                          {item.label}
                      </Link>
                  ),
                  style: { margin: 0, padding: 0 },
              },
    );

    const handleClickEvent = ({ key }: { key: string }) => {
        if (key === "logout") {
            logout();
        }
    };

    const firstLastName = `${user?.firstName?.split(" ")[0]} ${user?.lastName?.split(" ")[0]}`;

    return (
        <>
            <Dropdown
                menu={{ items: dropdownItems, onClick: handleClickEvent }}
                trigger={["click"]}
                className="hidden md:flex items-center gap-4"
            >
                <div className="flex gap-4 items-center cursor-pointer">
                    <div className="h-12 w-12 rounded-full bg-state-green-low flex justify-center items-center font-bold text-blue-full text-2xl">
                        GI
                    </div>
                    <div className="flex flex-col">
                        <span className="font-semibold text-blue-full">
                            {firstLastName === " " && !isHydrated
                                ? "Usuario"
                                : firstLastName}
                        </span>
                        <span className="text-black-medium text-sm lowercase">
                            {user?.role}
                        </span>
                    </div>
                    <ArrowDown />
                </div>
            </Dropdown>
        </>
    );
}
