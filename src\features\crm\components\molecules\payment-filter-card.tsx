import React from "react";
import { Card, DatePicker, Select, Radio, Button } from "antd";
import { Filter, RefreshCw } from "lucide-react";
import type { RangePickerProps } from "antd/es/date-picker";
import { PaymentCurrency } from "../../types/payment";

const { RangePicker } = DatePicker;
const { Option } = Select;

export type PaymentStatusFilter = "all" | "paid" | "pending";
export type CurrencyFilter = "all" | PaymentCurrency.PEN | PaymentCurrency.USD;
export type PaymentMethodFilter = "all" | string;

// Estructura simplificada que coincide con los datos mock actuales
export type SimplePaymentMethod = {
    pmid: string;
    name: string;
};

/**
 * Estado de los filtros de pago
 * @property dateRange - Rango de fechas seleccionado
 * @property paymentStatus - Estado del pago (todos, pagados, pendientes)
 * @property currency - Moneda (todas, soles, dólares)
 * @property paymentMethod - Método de pago (todos, o un ID específico)
 */
export interface PaymentFiltersState {
    dateRange: RangePickerProps["value"];
    paymentStatus: PaymentStatusFilter;
    currency: CurrencyFilter;
    paymentMethod: PaymentMethodFilter;
}

/**
 * Props para el componente PaymentFilterCard
 * @property paymentMethods - Lista de métodos de pago disponibles
 * @property filters - Estado actual de los filtros
 * @property onFilterChange - Función que se llama cuando cambia un filtro
 * @property onReset - Función opcional para restablecer todos los filtros
 */
interface PaymentFilterCardProps {
    paymentMethods: SimplePaymentMethod[];
    filters: PaymentFiltersState;
    onFilterChange: (filters: PaymentFiltersState) => void;
    onReset?: () => void;
}

/**
 * Componente de filtro para la sección de pagos
 * Permite filtrar por fecha, estado, moneda y método de pago
 */
const PaymentFilterCard: React.FC<PaymentFilterCardProps> = ({
    paymentMethods,
    filters,
    onFilterChange,
    onReset,
}) => {
    return (
        <Card className="mb-6">
            <div className="flex flex-wrap items-center gap-4 justify-between">
                <div className="flex items-center">
                    <Filter size={20} className="text-gray-500 mr-2" />
                    <span className="font-semibold text-lg mr-4">Filtros</span>
                </div>

                <div className="flex flex-wrap gap-4">
                    <div>
                        <div className="text-xs text-gray-500 mb-1">
                            Rango de fechas
                        </div>
                        <RangePicker
                            className="w-full sm:w-auto"
                            value={filters.dateRange}
                            onChange={(dates) =>
                                onFilterChange({ ...filters, dateRange: dates })
                            }
                        />
                    </div>

                    <div>
                        <div className="text-xs text-gray-500 mb-1">Estado</div>
                        <Radio.Group
                            value={filters.paymentStatus}
                            onChange={(e) =>
                                onFilterChange({
                                    ...filters,
                                    paymentStatus: e.target.value,
                                })
                            }
                        >
                            <Radio.Button value="all">Todos</Radio.Button>
                            <Radio.Button value="paid">Pagados</Radio.Button>
                            <Radio.Button value="pending">Pendientes</Radio.Button>
                        </Radio.Group>
                    </div>

                    <div>
                        <div className="text-xs text-gray-500 mb-1">Moneda</div>
                        <Select
                            value={filters.currency}
                            style={{ width: 120 }}
                            onChange={(value) =>
                                onFilterChange({ ...filters, currency: value })
                            }
                        >
                            <Option value="all">Todas</Option>
                            <Option value="pen">Soles</Option>
                            <Option value="usd">Dólares</Option>
                        </Select>
                    </div>

                    <div>
                        <div className="text-xs text-gray-500 mb-1">Método de pago</div>
                        <Select
                            value={filters.paymentMethod}
                            style={{ width: 180 }}
                            onChange={(value) =>
                                onFilterChange({ ...filters, paymentMethod: value })
                            }
                        >
                            <Option value="all">Todos</Option>
                            {paymentMethods.map((method) => (
                                <Option key={method.pmid} value={method.pmid}>
                                    {method.name}
                                </Option>
                            ))}
                        </Select>
                    </div>

                    {onReset && (
                        <div className="self-end">
                            <Button
                                icon={<RefreshCw size={16} />}
                                onClick={onReset}
                                type="default"
                            >
                                Restablecer
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </Card>
    );
};

export default PaymentFilterCard;
