import { useContacts, UseContactsQuery } from "@/features/crm/hooks/use-contact";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Select, SelectProps } from "antd";
import { ExternalLink, Plus } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import CreateContactForm from "@/features/crm/components/organisms/create-contact-form";
import { useDebounce } from "@hooks/use-debounce";
import type { OrderContact } from "../../types/order";

interface SelectContactProps extends Omit<SelectProps, "options"> {
    value?: string; // Add value prop for controlled component
    onChange?: (value: string) => void; // Add onChange prop
    defaultContact?: OrderContact; // Optional prop to display the selected contact name when not in options
}

export default function SelectContact({
    value,
    onChange,
    defaultContact,
    ...restProps
}: SelectContactProps) {
    const [query, setQuery] = useState<UseContactsQuery | null>(null);
    const debouncedQuery = useDebounce(query, 1000);
    const [modalOpen, setModalOpen] = useState(false);

    const { contacts, isLoading } = useContacts({
        page: 1,
        pageSize: 1000,
        query: {
            search: debouncedQuery?.search,
        },
    });

    // Crear opciones de contactos
    const contactsOptions: SelectProps["options"] = contacts?.map((contact) => ({
        value: contact.uid,
        label: contact.fullName,
        data: {
            info: contact,
        },
    }));

    const options = [...contactsOptions];
    if (value && defaultContact) {
        const isValueInOptions = contactsOptions.some(
            (option) => option.value === value,
        );
        if (!isValueInOptions) {
            options.unshift({
                value: value,
                label: defaultContact.fullName,
                data: {
                    info: {
                        uid: value,
                        fullName: defaultContact.fullName,
                        phoneNumber: defaultContact.phoneNumber,
                    },
                },
            });
        }
    }

    const handleCloseModal = () => {
        setModalOpen(false);
    };

    const handleSearch = (value: string) => {
        setQuery({ search: value });
    };

    const handleClear = () => {
        setQuery(null);
    };

    return (
        <>
            <Modal
                title={
                    <div className="text-lg font-semibold text-center">
                        Crear Contacto
                    </div>
                }
                footer={false}
                open={modalOpen}
                centered
                onCancel={() => {
                    setModalOpen(false);
                }}
            >
                <CreateContactForm handleCloseModal={handleCloseModal} />
            </Modal>
            <Select
                {...restProps}
                value={value}
                onChange={onChange}
                onSearch={handleSearch}
                onClear={handleClear}
                options={options}
                placeholder="Buscar por nombre, email o teléfono"
                optionRender={(option) => (
                    <div className="flex justify-between items-center">
                        <div className="flex flex-col">
                            <span>{option.data.label}</span>
                            {option.data.data.info.phoneNumber && (
                                <span className="text-xs text-gray-600">
                                    {option.data.data.info.phoneNumber}
                                </span>
                            )}
                        </div>
                        <Link
                            to={`/crm/contacts/${option.data.value}`}
                            title="View Contact"
                        >
                            <ExternalLink size={14} />
                        </Link>
                    </div>
                )}
                loading={isLoading}
                filterOption={false}
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras el contacto?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
        </>
    );
}
