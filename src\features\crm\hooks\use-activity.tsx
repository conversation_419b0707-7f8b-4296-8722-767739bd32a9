import { useMutation, useQuery } from "@tanstack/react-query";
import {
    createActivity,
    listActivities,
    retrieveActivity,
    updateActivity,
    deleteActivity,
} from "../services/portals/activity";
import { AxiosError } from "axios";
import {
    ActivityQueryParams,
    ActivityCreate,
    ActivityUpdate,
    ActivityRetrieve,
} from "../types/activity";
import { App } from "antd";
import queryClient from "@lib/queryClient";

type UseActivitiesProps = {
    queryParams?: ActivityQueryParams;
    enabled?: boolean;
};

export const useActivities = ({ enabled, queryParams }: UseActivitiesProps = {}) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["activities", queryParams],
        queryFn: () => listActivities(queryParams),
        enabled,
    });

    const { count, results: activities } = data || {
        count: 0,
        results: [],
    };

    return {
        isLoading,
        isError,
        activities,
        count,
    };
};

export const useActivity = (aid: string) => {
    const { data, isLoading, isError } = useQuery({
        queryKey: ["activity", aid],
        queryFn: () => retrieveActivity(aid),
        enabled: !!aid,
    });

    return {
        isLoading,
        isError,
        activity: data,
    };
};

type UseCreateActivityProps = {
    onCreateActivitySuccess?: () => void;
    onCreateActivityError?: () => void;
};

export const useCreateActivity = ({
    onCreateActivitySuccess,
    onCreateActivityError,
}: UseCreateActivityProps = {}) => {
    const { message, notification } = App.useApp();
    return useMutation<ActivityRetrieve, AxiosError, ActivityCreate>({
        mutationFn: (newActivity) => createActivity(newActivity),
        onSuccess: () => {
            message.success({
                content: "Actividad creada exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["activities"],
            });
            onCreateActivitySuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al crear la actividad",
                description: "Ha ocurrido un error al intentar crear la actividad",
                duration: 2,
            });
            onCreateActivityError?.();
        },
    });
};

type UseUpdateActivityProps = {
    onUpdateActivitySuccess?: () => void;
    onUpdateActivityError?: () => void;
};

export const useUpdateActivity = ({
    onUpdateActivitySuccess,
    onUpdateActivityError,
}: UseUpdateActivityProps = {}) => {
    const { message, notification } = App.useApp();
    return useMutation<
        ActivityRetrieve,
        AxiosError,
        { aid: string; activity: ActivityUpdate }
    >({
        mutationFn: ({ aid, activity }) => updateActivity(aid, activity),
        onSuccess: () => {
            message.success({
                content: "Actividad actualizada exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["activities"],
            });
            queryClient.invalidateQueries({
                queryKey: ["activity"],
            });
            onUpdateActivitySuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al actualizar la actividad",
                description: "Ha ocurrido un error al intentar actualizar la actividad",
                duration: 2,
            });
            onUpdateActivityError?.();
        },
    });
};

type UseDeleteActivityProps = {
    onDeleteActivitySuccess?: () => void;
    onDeleteActivityError?: () => void;
};

export const useDeleteActivity = ({
    onDeleteActivitySuccess,
    onDeleteActivityError,
}: UseDeleteActivityProps = {}) => {
    const { message, notification } = App.useApp();
    return useMutation<void, AxiosError, string>({
        mutationFn: (aid) => deleteActivity(aid),
        onSuccess: () => {
            message.success({
                content: "Actividad eliminada exitosamente",
                duration: 2,
            });
            queryClient.invalidateQueries({
                queryKey: ["activities"],
            });
            onDeleteActivitySuccess?.();
        },
        onError: () => {
            notification.error({
                message: "Error al eliminar la actividad",
                description: "Ha ocurrido un error al intentar eliminar la actividad",
                duration: 2,
            });
            onDeleteActivityError?.();
        },
    });
};
