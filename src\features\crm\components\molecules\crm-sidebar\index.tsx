import { Link, useLocation } from "react-router-dom";
import { Menu } from "antd";
import type { MenuProps } from "antd";

import Dashboard from "@assets/icons/huge/dashboard.svg?react";
import ContactBook from "@assets/icons/huge/contact-book.svg?react";
import TaskDaily from "@assets/icons/huge/task-daily.svg?react";
import ShoppingBasket from "@assets/icons/huge/shopping-basket.svg?react";
import MoneyReceive from "@assets/icons/huge/money-receive.svg?react";
import { CalendarRange, Ticket } from "lucide-react";
type MenuItem = Required<MenuProps>["items"][number];

const items: MenuItem[] = [
    {
        key: "general",
        label: <span className="text-black-medium font-semibold text-xs">GENERAL</span>,
        type: "group",
        children: [
            {
                key: "dashboard",
                label: <Link to="/crm">Dashboard</Link>,
                icon: <Dashboard />,
            },
        ],
    },
    {
        type: "divider",
    },
    {
        key: "leads-group",
        label: <span className="text-black-medium font-semibold text-xs">LEADS</span>,
        type: "group",
        children: [
            {
                key: "contacts",
                label: (
                    <Link to="/crm/contacts" className="font-medium text-sm">
                        Contactos
                    </Link>
                ),
                icon: <ContactBook />,
            },
            {
                key: "activities",
                label: (
                    <Link to="/crm/activities" className="font-medium text-sm">
                        Actividades
                    </Link>
                ),
                icon: <TaskDaily />,
            },
        ],
    },
    {
        key: "sales",
        label: (
            <span className="text-black-medium font-semibold text-xs">
                GESTIÓN DE VENTAS
            </span>
        ),
        type: "group",
        children: [
            {
                key: "orders",
                label: (
                    <Link to="/crm/orders" className="font-medium">
                        Órdenes
                    </Link>
                ),
                icon: <ShoppingBasket />,
            },
            {
                key: "payments",
                label: (
                    <Link to="/crm/payments" className="font-medium">
                        Págos
                    </Link>
                ),
                icon: <MoneyReceive />,
            },
        ],
    },
    {
        key: "events",
        label: (
            <span className="text-black-medium font-semibold text-xs">
                GESTIÓN DE EVENTOS
            </span>
        ),
        type: "group",
        children: [
            {
                key: "events",
                label: (
                    <Link to="/crm/events" className="font-medium">
                        Eventos
                    </Link>
                ),
                icon: <Ticket />,
            },
            {
                key: "event-schedules",
                label: (
                    <Link to="/crm/event-schedules" className="font-medium">
                        Horarios
                    </Link>
                ),
                icon: <CalendarRange />,
            },
        ],
    },
];

const PATHS: Record<string, string[]> = {
    "/crm": ["dashboard"],
    "/crm/contacts": ["contacts"],
    "/crm/activities": ["activities"],
    "/crm/orders": ["orders"],
    "/crm/payments": ["payments"],
    "/crm/events": ["events"],
    "/crm/event-schedules": ["event-schedules"],
};

export default function CrmSidebar() {
    const location = useLocation();

    const selectedKeys =
        location.pathname === "/crm"
            ? ["dashboard"]
            : Object.entries(PATHS).find(([path]) => {
                  if (path === "/crm") return false;
                  return location.pathname.includes(path);
              })?.[1] || [];

    const onClick: MenuProps["onClick"] = (e) => {
        console.info("click ", e);
    };

    return (
        <Menu
            onClick={onClick}
            style={{ width: 256, border: 0 }}
            defaultSelectedKeys={selectedKeys}
            defaultOpenKeys={[]}
            mode="inline"
            items={items}
            className="fixed w-64"
        />
    );
}
