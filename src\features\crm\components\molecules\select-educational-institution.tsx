import { useState } from "react";
import {
    createEducationalInstitution,
    getEducationalInstitutions,
} from "@/features/crm/services/portals/educational-institution";
import { useMutation, useQuery } from "@tanstack/react-query";
import { App, Button, Divider, Form, Input, Modal, Select, SelectProps } from "antd";
import { Plus } from "lucide-react";
import {
    CreateEducationalInstitutionValues,
    EducationalInstitutionTypeLabel,
} from "../../types/educational-institution";
import queryClient from "@lib/queryClient";
import FormLabel from "../atoms/FormLabel";

interface SelectEducationalInstitutionProps extends Omit<SelectProps, "options"> {
    value?: string;
    onChange?: (value: string) => void;
}

export default function SelectEducationalInstitution({
    value,
    onChange,
    ...restProps
}: SelectEducationalInstitutionProps) {
    const [modalOpen, setModalOpen] = useState(false);
    const { data, isLoading } = useQuery({
        queryKey: ["educational-institutions"],
        queryFn: async () => getEducationalInstitutions(),
    });

    const { results: educationalInstitutions } = data || {
        results: [],
    };

    const [form] = Form.useForm<CreateEducationalInstitutionValues>();
    const { message } = App.useApp();

    const { mutate: createMutate, isPending } = useMutation({
        mutationFn: createEducationalInstitution,
        onSuccess: (newInstitution) => {
            message.success("Institución educativa creada exitosamente");
            queryClient.invalidateQueries({ queryKey: ["educational-institutions"] });
            setModalOpen(false);
            form.resetFields();

            // Automatically select the newly created institution
            if (onChange && newInstitution?.eiid) {
                onChange(newInstitution.eiid);
            }
        },
        onError: (error) => {
            console.error("Error creating educational institution:", error);
            message.error("Error al crear la institución educativa");
        },
    });

    const educationalInstitutionsOptions: SelectProps["options"] =
        educationalInstitutions?.map((educationalInstitution) => ({
            value: educationalInstitution.eiid,
            label: educationalInstitution.name,
        })) || [];

    const handleFormFinish = (values: CreateEducationalInstitutionValues) => {
        createMutate(values);
    };

    // This is the critical fix - ensure we're properly handling the value changes
    const handleChange = (selectedValue: string) => {
        if (onChange) {
            onChange(selectedValue);
        }
    };

    return (
        <>
            <Select
                {...restProps}
                value={value}
                onChange={handleChange}
                options={educationalInstitutionsOptions}
                loading={isLoading}
                optionFilterProp="label"
                allowClear
                showSearch
                dropdownRender={(menu) => (
                    <>
                        {menu}
                        <Divider className="my-1" />
                        <div className="flex justify-between items-center px-2">
                            <p className="text-sm text-gray-700 font-medium">
                                ¿No encuentras la Institución?
                            </p>
                            <Button
                                size="small"
                                type="primary"
                                icon={<Plus size={12} />}
                                onClick={() => setModalOpen(true)}
                            >
                                Agregar
                            </Button>
                        </div>
                    </>
                )}
            />
            <Modal
                title="Agregar Institución"
                footer={null}
                open={modalOpen}
                onCancel={() => {
                    setModalOpen(false);
                    form.resetFields();
                }}
            >
                <Form
                    name="create-educational-institution"
                    form={form}
                    layout="vertical"
                    onFinish={handleFormFinish}
                >
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="name"
                        rules={[
                            {
                                required: true,
                                message:
                                    "Por favor ingrese el nombre de la institución",
                            },
                        ]}
                        label={<FormLabel>Nombre de la Institución</FormLabel>}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="institutionType"
                        label={<FormLabel>Tipo de Institución</FormLabel>}
                    >
                        <Select
                            allowClear
                            placeholder="Seleccione el tipo de institución"
                            options={Object.entries(
                                EducationalInstitutionTypeLabel,
                            ).map(([value, label]) => ({
                                value,
                                label,
                            }))}
                        />
                    </Form.Item>
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="acronym"
                        label={<FormLabel>Acrónimo</FormLabel>}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="country"
                        label={<FormLabel>País</FormLabel>}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="region"
                        label={<FormLabel>Departamento/Estado</FormLabel>}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item<CreateEducationalInstitutionValues>
                        name="city"
                        label={<FormLabel>Ciudad</FormLabel>}
                    >
                        <Input />
                    </Form.Item>
                    <div className="flex gap-2 justify-end">
                        <Button
                            type="default"
                            disabled={isPending}
                            onClick={() => {
                                setModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button type="primary" htmlType="submit" loading={isPending}>
                            Agregar Institución
                        </Button>
                    </div>
                </Form>
            </Modal>
        </>
    );
}