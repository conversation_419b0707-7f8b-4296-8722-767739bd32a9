import { Route, Routes } from "react-router-dom";
import LoginPage from "@pages/shared/LoginPage";
import LogoutPage from "@pages/shared/LogoutPage";

import ProtectedOutlet from "./ProtectedOutlet";
import ErpRoutes from "./erp/ErpRoutes";
import CrmRoutes from "@/features/crm/route";
import CmsRoutes from "./cms/CmsRoutes";
import LmsRoutes from "./lms/LmsRoutes";
import HomePage from "@pages/shared/HomePage";

export default function MainRoutes() {
    return (
        <Routes>
            {/** Public Routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/logout" element={<LogoutPage />} />

            {/** Private Routes */}
            <Route element={<ProtectedOutlet />}>
                <Route path="/" element={<HomePage />} />

                {/** Core Routes */}
                <Route path="/erp/*" element={<ErpRoutes />} />

                {/** CRM Routes */}
                <Route path="/crm/*" element={<CrmRoutes />} />

                {/** LMS Routes */}
                <Route path="/lms/*" element={<LmsRoutes />} />

                {/** CMS Routes */}
                <Route path="/cms/*" element={<CmsRoutes />} />

                {/** Admin Routes */}
                {/* <Route path="/admin/*" element={<AdminRoutes />} /> */}
            </Route>
        </Routes>
    );
}
